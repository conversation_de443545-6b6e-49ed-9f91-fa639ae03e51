#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ES 数据诊断脚本
用于检查源环境和目标环境的数据差异，分析同步问题
"""

import json
import requests
from requests.auth import HTTPBasicAuth
from datetime import datetime, timezone, timedelta
import urllib3

# 禁用 InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 定义UTC+8时区
UTC_PLUS_8 = timezone(timedelta(hours=8))

# 配置
SRC_HOSTS = [
    "http://**********:9200",
    "http://**********:9200",
    "http://**********:9200",
]
SRC_AUTH = HTTPBasicAuth("elastic", "W8DOwJ2xs4mBV4BcNBNi")

DST_HOST = "http://***********:9200"
DST_AUTH = HTTPBasicAuth("elastic", "elastic")

INDICES_TO_CHECK = {
    "chn_ylcg": "create_time",
    "markersweb_attachment_analysis_alias": "insert_time",
}


def get_available_src_host():
    """获取可用的源主机"""
    for host in SRC_HOSTS:
        try:
            response = requests.get(
                f"{host}/_cluster/health", auth=SRC_AUTH, timeout=10, verify=False
            )
            if response.status_code == 200:
                print(f"✓ 使用源主机: {host}")
                return host
        except:
            continue
    return None


def get_index_stats(host, auth, index_name):
    """获取索引统计信息"""
    try:
        # 获取文档总数
        count_response = requests.get(
            f"{host}/{index_name}/_count", auth=auth, verify=False
        )
        if count_response.status_code != 200:
            return None

        total_count = count_response.json().get("count", 0)

        # 获取索引映射信息
        mapping_response = requests.get(
            f"{host}/{index_name}/_mapping", auth=auth, verify=False
        )
        mapping_info = "未知"
        if mapping_response.status_code == 200:
            mapping_data = mapping_response.json()
            # 获取第一个索引的映射（处理别名情况）
            first_index = list(mapping_data.keys())[0]
            properties = (
                mapping_data[first_index].get("mappings", {}).get("properties", {})
            )
            mapping_info = {
                k: properties[k].get("type", "unknown")
                for k in properties.keys()
                if k in ["create_time", "insert_time"]
            }

        return {"total_count": total_count, "mapping": mapping_info}
    except Exception as e:
        print(f"获取索引 {index_name} 统计失败: {e}")
        return None


def get_sample_documents(host, auth, index_name, timestamp_field, size=5):
    """获取样本文档"""
    try:
        # 获取最新的几个文档
        query = {
            "query": {"match_all": {}},
            "sort": [{"_id": {"order": "desc"}}],
            "size": size,
            "_source": [timestamp_field, "_id"],
        }

        response = requests.post(
            f"{host}/{index_name}/_search", auth=auth, json=query, verify=False
        )
        if response.status_code != 200:
            return []

        hits = response.json().get("hits", {}).get("hits", [])
        samples = []
        for hit in hits:
            samples.append(
                {
                    "id": hit["_id"],
                    "timestamp": hit.get("_source", {}).get(timestamp_field, "无"),
                }
            )
        return samples
    except Exception as e:
        print(f"获取样本文档失败: {e}")
        return []


def check_time_range_data(host, auth, index_name, timestamp_field, hours_back=24):
    """检查指定时间范围内的数据"""
    try:
        end_time = datetime.now(UTC_PLUS_8)
        start_time = end_time - timedelta(hours=hours_back)

        # 先获取样本数据判断时间格式
        sample_query = {
            "query": {"match_all": {}},
            "size": 1,
            "_source": [timestamp_field],
        }

        sample_response = requests.post(
            f"{host}/{index_name}/_search", auth=auth, json=sample_query, verify=False
        )
        if sample_response.status_code != 200:
            return 0

        sample_data = sample_response.json()
        sample_hits = sample_data.get("hits", {}).get("hits", [])

        if not sample_hits:
            return 0

        sample_timestamp = sample_hits[0].get("_source", {}).get(timestamp_field)

        # 根据样本数据格式构建查询
        if isinstance(sample_timestamp, str):
            if len(sample_timestamp) == 10 and sample_timestamp.count("-") == 2:
                # 日期格式 YYYY-MM-DD
                start_str = start_time.strftime("%Y-%m-%d")
                end_str = (end_time + timedelta(days=1)).strftime("%Y-%m-%d")
            else:
                # 日期时间格式 YYYY-MM-DD HH:MM:SS
                start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
                end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")

            query = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "range": {
                                    f"{timestamp_field}.keyword": {
                                        "gte": start_str,
                                        "lt": end_str,
                                    }
                                }
                            },
                            {
                                "range": {
                                    timestamp_field: {"gte": start_str, "lt": end_str}
                                }
                            },
                        ],
                        "minimum_should_match": 1,
                    }
                }
            }
        else:
            # 时间戳格式
            start_epoch = int(start_time.timestamp() * 1000)
            end_epoch = int(end_time.timestamp() * 1000)
            query = {
                "query": {
                    "range": {
                        timestamp_field: {
                            "gte": start_epoch,
                            "lt": end_epoch,
                            "format": "epoch_millis",
                        }
                    }
                }
            }

        count_response = requests.post(
            f"{host}/{index_name}/_count", auth=auth, json=query, verify=False
        )
        if count_response.status_code == 200:
            return count_response.json().get("count", 0)
        return 0

    except Exception as e:
        print(f"检查时间范围数据失败: {e}")
        return 0


def check_duplicate_ids(host, auth, index_name, sample_size=1000):
    """检查是否有重复的文档ID"""
    try:
        # 获取样本文档ID
        query = {"query": {"match_all": {}}, "size": sample_size, "_source": False}

        response = requests.post(
            f"{host}/{index_name}/_search", auth=auth, json=query, verify=False
        )
        if response.status_code != 200:
            return {"total_checked": 0, "unique_ids": 0, "duplicates": 0}

        hits = response.json().get("hits", {}).get("hits", [])
        ids = [hit["_id"] for hit in hits]
        unique_ids = set(ids)

        return {
            "total_checked": len(ids),
            "unique_ids": len(unique_ids),
            "duplicates": len(ids) - len(unique_ids),
        }
    except Exception as e:
        print(f"检查重复ID失败: {e}")
        return {"total_checked": 0, "unique_ids": 0, "duplicates": 0}


def main():
    print("=" * 60)
    print("ES 数据诊断报告")
    print(f"诊断时间: {datetime.now(UTC_PLUS_8).strftime('%Y-%m-%d %H:%M:%S')} (UTC+8)")
    print("=" * 60)

    src_host = get_available_src_host()
    if not src_host:
        print("❌ 无法连接到源环境")
        return

    for index_name, timestamp_field in INDICES_TO_CHECK.items():
        print(f"\n📊 检查索引: {index_name}")
        print("-" * 40)

        # 检查源环境
        print("🔍 源环境 (正式环境):")
        src_stats = get_index_stats(src_host, SRC_AUTH, index_name)
        if src_stats:
            print(f"  总文档数: {src_stats['total_count']:,}")
            print(f"  字段映射: {src_stats['mapping']}")

            # 获取样本文档
            src_samples = get_sample_documents(
                src_host, SRC_AUTH, index_name, timestamp_field
            )
            if src_samples:
                print("  样本文档:")
                for sample in src_samples[:3]:
                    print(
                        f"    ID: {sample['id']}, {timestamp_field}: {sample['timestamp']}"
                    )

            # 检查最近24小时数据
            recent_count = check_time_range_data(
                src_host, SRC_AUTH, index_name, timestamp_field, 24
            )
            print(f"  最近24小时数据: {recent_count:,} 条")

            # 检查重复ID
            dup_info = check_duplicate_ids(src_host, SRC_AUTH, index_name)
            print(
                f"  重复检查 (样本{dup_info['total_checked']}条): 唯一ID {dup_info['unique_ids']}, 重复 {dup_info['duplicates']}"
            )
        else:
            print("  ❌ 无法获取统计信息")

        # 检查目标环境
        print("\n🎯 目标环境 (测试环境):")
        dst_stats = get_index_stats(DST_HOST, DST_AUTH, index_name)
        if dst_stats:
            print(f"  总文档数: {dst_stats['total_count']:,}")
            print(f"  字段映射: {dst_stats['mapping']}")

            # 获取样本文档
            dst_samples = get_sample_documents(
                DST_HOST, DST_AUTH, index_name, timestamp_field
            )
            if dst_samples:
                print("  样本文档:")
                for sample in dst_samples[:3]:
                    print(
                        f"    ID: {sample['id']}, {timestamp_field}: {sample['timestamp']}"
                    )

            # 检查最近24小时数据
            recent_count = check_time_range_data(
                DST_HOST, DST_AUTH, index_name, timestamp_field, 24
            )
            print(f"  最近24小时数据: {recent_count:,} 条")

            # 检查重复ID
            dup_info = check_duplicate_ids(DST_HOST, DST_AUTH, index_name)
            print(
                f"  重复检查 (样本{dup_info['total_checked']}条): 唯一ID {dup_info['unique_ids']}, 重复 {dup_info['duplicates']}"
            )
        else:
            print("  ❌ 无法获取统计信息")

        # 对比分析
        if src_stats and dst_stats:
            print("\n📈 对比分析:")
            diff = dst_stats["total_count"] - src_stats["total_count"]
            if diff > 0:
                print(f"  ⚠️  目标环境比源环境多 {diff:,} 条数据")
                if diff > src_stats["total_count"] * 0.1:  # 超过10%
                    print("  🚨 数据差异过大，可能存在重复同步问题")
            elif diff < 0:
                print(f"  ℹ️  目标环境比源环境少 {abs(diff):,} 条数据")
            else:
                print("  ✅ 两环境数据量一致")


if __name__ == "__main__":
    main()
