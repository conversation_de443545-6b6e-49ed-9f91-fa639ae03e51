#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
from elasticsearch import Elasticsearch
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Standard fields list
STANDARD_FIELDS = (
    "bid_name",  # 标段名称
    "bid_number",  # 标段编号
    "bid_budget",  # 标段预算金额
    "fiscal_delegation_number",  # 财政委托编号
    "prj_addr",  # 招标项目地址
    "prj_name",  # 招标项目名称
    "prj_number",  # 招标项目编号
    "prj_type",  # 招标项目类型
    "release_time",  # 发布日期
    "prj_approval_authority",  # 项目审批单位
    "superintendent_office",  # 监督部门
    "superintendent_office_code",  # 监督部门编号
    "tenderee",  # 招标人
    "bid_submission_deadline",  # 投标截止时间
    "trade_platform",  # 交易平台
    "procurement_method",  # 采购方式
    "prj_sub_type",  # 项目细分类型
    "province",  # 省份
    "city",  # 城市
    "county",  # 区县
    "announcement_type",
    "object_name",  # 标的物名称
    "object_brand",  # 标的物品牌
    "object_model",  # 标的物型号
    "object_amount",  # 标的物数量
    "object_unit",  # 标的物单位
    "object_oem",  # 标的物生产厂家
    "object_supplier",  # 标的物中标供应商
    "object_price",  # 标的物中标单价
    "object_total_price",  # 标的物中标总价
    "object_maintenance_period",  # 标的物维保期限
    "object_price_source",  # 标的物价格来源
    "object_quality",  # 标的物质量层次
    "object_produce_area",  # 标的物产地
    "object_conf",  # 标的物配置参数
    "bidder_price",  # 中标金额
    "bidder_name",  # 中标单位名称
    "bidder_contact_person",  # 中标单位联系人
    "bidder_contact_phone_number",  # 中标单位联系人电话
    "bidder_contract_config_param",  # 中标合同配置参数
    "agent",  # 代理机构
    "service_fee",  # 代理服务收费金额
    "bid_cancelled_flag",  # 标段是否废标标记
    "bid_cancelled_reason",  # 标段废标原因
    "source_id",  # 源文档ID
    "source_title",  # 源文档标题
    "source_create_time",  # 源文档创建时间
    "source_category",  # 源文档分类
    "source_url",  # 源文档URL
    "source_appendix",  # 源文档附件信息
    "appendix_info",  # 所有附件文件信息（包含上传ID）
    "bid_doc_name",  # 招标文件名称
    "bid_doc_ext",  # 招标文件扩展名
    "bid_doc_link_out",  # 招标文件外部链接
    "bid_doc_link_key",  # 招标文件上传ID
    "contract_name",  # 合同文件名称
    "contract_ext",  # 合同文件扩展名
    "contract_link_out",  # 合同文件外部链接
    "contract_link_key",  # 合同文件上传ID
    "insert_time",  # 插入时间
)


def connect_to_elasticsearch():
    """连接到Elasticsearch"""
    es_host = os.getenv("ES_HOST")
    es_user = os.getenv("ES_USER")
    es_password = os.getenv("ES_PASSWORD")

    # 创建Elasticsearch客户端
    try:
        # 尝试使用较新版本的Elasticsearch客户端
        es = Elasticsearch(
            [es_host], basic_auth=(es_user, es_password), request_timeout=30
        )
    except TypeError:
        # 回退到旧版本的Elasticsearch客户端
        es = Elasticsearch([es_host], http_auth=(es_user, es_password))

    return es


def find_nonstandard_fields():
    """查找包含非标准字段的文档"""
    print("正在连接到Elasticsearch...")
    es = connect_to_elasticsearch()

    index_name = os.getenv(
        "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
    )
    print(f"正在查询索引: {index_name}")

    # 查询参数
    query = {"size": 100, "query": {"match_all": {}}}  # 可以根据需要调整大小

    # 执行查询
    response = es.search(index=index_name, body=query)

    # 处理结果
    nonstandard_fields_stats = {}  # 统计非标准字段出现次数
    documents_with_nonstandard_fields = []  # 包含非标准字段的文档

    print(f"\n共找到 {len(response['hits']['hits'])} 个文档")

    for hit in response["hits"]["hits"]:
        doc_id = hit["_id"]
        source = hit["_source"]

        # 查找不在STANDARD_FIELDS中的字段
        extra_fields = [
            field for field in source.keys() if field not in STANDARD_FIELDS
        ]

        if extra_fields:
            # 记录包含非标准字段的文档
            doc_info = {
                "doc_id": doc_id,
                "nonstandard_fields": extra_fields,
                "source_data": source,
            }
            documents_with_nonstandard_fields.append(doc_info)

            # 统计非标准字段出现次数
            for field in extra_fields:
                if field not in nonstandard_fields_stats:
                    nonstandard_fields_stats[field] = 0
                nonstandard_fields_stats[field] += 1

    # 输出结果
    print(f"\n找到 {len(documents_with_nonstandard_fields)} 个包含非标准字段的文档")

    if nonstandard_fields_stats:
        print("\n非标准字段统计:")
        for field, count in sorted(
            nonstandard_fields_stats.items(), key=lambda x: x[1], reverse=True
        ):
            print(f"- {field}: {count} 次出现")

    # 打印包含非标准字段的文档详情
    print("\n包含非标准字段的文档详情:")
    for i, doc in enumerate(documents_with_nonstandard_fields, 1):
        print(f"\n文档 {i}:")
        print(f"  文档ID: {doc['doc_id']}")
        print(f"  非标准字段: {', '.join(doc['nonstandard_fields'])}")
        print("  文档数据:")

        # 打印文档中的所有字段和值
        for field, value in doc["source_data"].items():
            # 标记非标准字段
            field_marker = " (非标准)" if field in doc["nonstandard_fields"] else ""
            print(f"    {field}{field_marker}: {value}")

    # 保存结果到JSON文件
    output_file = "nonstandard_fields_report.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(
            {
                "summary": {
                    "total_documents": len(response["hits"]["hits"]),
                    "documents_with_nonstandard_fields": len(
                        documents_with_nonstandard_fields
                    ),
                    "nonstandard_fields": nonstandard_fields_stats,
                },
                "documents": documents_with_nonstandard_fields,
            },
            f,
            ensure_ascii=False,
            indent=2,
        )

    print(f"\n详细报告已保存到 {output_file}")


if __name__ == "__main__":
    find_nonstandard_fields()
