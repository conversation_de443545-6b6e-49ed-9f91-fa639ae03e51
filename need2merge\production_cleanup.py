#!/usr/bin/env python3
"""
生产环境索引字段清理脚本
使用 .env 文件中的生产环境配置
"""

import os
from dotenv import load_dotenv
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk

# 加载环境变量
load_dotenv()

# 标准字段列表
STANDARD_FIELDS = {
    # 业务数据字段（40个）
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    # 源数据元数据字段（6个）
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    # 附件相关字段（8个）
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    # 系统字段（1个）
    "insert_time",
}


def setup_production_es():
    """设置生产环境ES连接"""
    es_host = os.getenv("ES_HOST", "http://**********:9200")
    es_user = os.getenv("ES_USER", "elastic")
    es_password = os.getenv("ES_PASSWORD", "W8DOwJ2xs4mBV4BcNBNi")

    print(f"连接到生产环境 Elasticsearch: {es_host}")
    print(f"用户: {es_user}")

    try:
        # 尝试新版本的参数
        es = Elasticsearch(
            [es_host],
            basic_auth=(es_user, es_password),
            request_timeout=60,
            max_retries=3,
            retry_on_timeout=True,
        )
    except TypeError:
        # 兼容旧版本
        es = Elasticsearch([es_host], http_auth=(es_user, es_password))

    # 测试连接
    try:
        info = es.info()
        print(f"✓ 成功连接到生产环境 Elasticsearch {info['version']['number']}")
        return es
    except Exception as e:
        print(f"✗ 连接生产环境失败: {e}")
        return None


def analyze_production_index():
    """分析生产环境索引"""
    es = setup_production_es()
    if not es:
        return None, None

    index_name = os.getenv(
        "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
    )

    print(f"\n分析生产环境索引: {index_name}")
    print("=" * 60)

    try:
        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            print(f"✗ 生产环境索引 {index_name} 不存在")
            return None, None

        # 获取文档总数
        count_response = es.count(index=index_name)
        total_docs = count_response["count"]
        print(f"生产环境文档总数: {total_docs:,}")

        # 分析字段（使用较小的样本以避免性能影响）
        print(f"\n分析字段结构（样本：10000个文档）...")
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10000}
        )

        all_doc_fields = set()
        nonstandard_fields_count = {}

        for doc in sample_response["hits"]["hits"]:
            doc_fields = set(doc["_source"].keys())
            all_doc_fields.update(doc_fields)

            # 统计非标准字段
            for field in doc_fields:
                if field not in STANDARD_FIELDS:
                    nonstandard_fields_count[field] = (
                        nonstandard_fields_count.get(field, 0) + 1
                    )

        nonstandard_fields = all_doc_fields - STANDARD_FIELDS

        print(f"样本文档数: {len(sample_response['hits']['hits'])}")
        print(f"文档中总字段数: {len(all_doc_fields)}")
        print(f"标准字段数: {len(all_doc_fields & STANDARD_FIELDS)}")
        print(f"非标准字段数: {len(nonstandard_fields)}")

        if nonstandard_fields:
            print(f"\n非标准字段分布:")
            for field in sorted(nonstandard_fields):
                count = nonstandard_fields_count.get(field, 0)
                percentage = (count / len(sample_response["hits"]["hits"])) * 100
                print(
                    f"  - {field}: {count}/{len(sample_response['hits']['hits'])} 文档 ({percentage:.1f}%)"
                )
        else:
            print("✓ 样本中没有发现非标准字段")

        return es, nonstandard_fields

    except Exception as e:
        print(f"分析生产环境索引失败: {e}")
        return None, None


def cleanup_production_index():
    """清理生产环境索引"""
    es, nonstandard_fields = analyze_production_index()

    if not es:
        print("无法连接到生产环境，清理终止")
        return

    if not nonstandard_fields:
        print("✓ 生产环境索引已经是标准字段，无需清理")
        return

    index_name = os.getenv(
        "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
    )

    print(f"\n准备清理生产环境索引")
    print("=" * 60)
    print(f"索引: {index_name}")
    print(f"将要删除的非标准字段数: {len(nonstandard_fields)}")

    # 显示将要删除的字段
    print(f"\n将要删除的字段:")
    for i, field in enumerate(sorted(nonstandard_fields), 1):
        print(f"  {i:2d}. {field}")

    # 多重确认
    print(f"\n⚠️  警告：这是生产环境操作！")
    print(f"⚠️  将会修改生产环境的数据！")

    confirm1 = input(
        f"\n第一次确认 - 确认要清理生产环境的 {len(nonstandard_fields)} 个非标准字段吗? (yes/no): "
    )
    if confirm1.lower() != "yes":
        print("操作已取消")
        return

    confirm2 = input(f"第二次确认 - 这是生产环境，确认继续吗? (YES/no): ")
    if confirm2 != "YES":
        print("操作已取消")
        return

    # 执行清理
    print(f"\n开始清理生产环境...")

    processed_docs = 0
    updated_docs = 0
    errors = 0
    batch_size = 50  # 生产环境使用较小的批次大小

    try:
        # 扫描所有文档
        query = {"query": {"match_all": {}}}
        actions = []

        print("开始扫描文档...")

        for doc in scan(es, query=query, index=index_name, size=batch_size):
            processed_docs += 1
            doc_id = doc["_id"]
            doc_source = doc["_source"]

            # 找出需要删除的非标准字段
            fields_to_remove = []
            for field in doc_source.keys():
                if field not in STANDARD_FIELDS:
                    fields_to_remove.append(field)

            if fields_to_remove:
                # 构建只包含标准字段的新文档
                cleaned_source = {}
                for field, value in doc_source.items():
                    if field in STANDARD_FIELDS:
                        cleaned_source[field] = value

                # 使用 index 操作替换整个文档
                action = {
                    "_op_type": "index",
                    "_index": index_name,
                    "_id": doc_id,
                    "_source": cleaned_source,
                }

                actions.append(action)
                updated_docs += 1

                if len(actions) >= batch_size:
                    # 执行批量更新
                    try:
                        success_count, failed_items = bulk(
                            es,
                            actions,
                            request_timeout=120,  # 生产环境使用更长的超时时间
                            max_retries=3,
                        )
                        if failed_items:
                            errors += len(failed_items)
                            print(f"批量更新中有 {len(failed_items)} 个失败")
                    except Exception as e:
                        print(f"批量更新失败: {e}")
                        errors += len(actions)

                    actions = []

                    # 显示进度（每1000个文档显示一次）
                    if processed_docs % 1000 == 0:
                        print(
                            f"进度: 已处理 {processed_docs:,} 文档, 已更新 {updated_docs:,} 文档"
                        )

        # 处理剩余的操作
        if actions:
            try:
                success_count, failed_items = bulk(
                    es, actions, request_timeout=120, max_retries=3
                )
                if failed_items:
                    errors += len(failed_items)
                    print(f"最后批次更新中有 {len(failed_items)} 个失败")
            except Exception as e:
                print(f"最后批次更新失败: {e}")
                errors += len(actions)

        # 刷新索引
        print("刷新索引...")
        es.indices.refresh(index=index_name)

        print(f"\n生产环境清理完成!")
        print(f"总处理文档数: {processed_docs:,}")
        print(f"需要更新的文档数: {updated_docs:,}")
        print(f"失败的文档数: {errors:,}")

        if errors == 0:
            print("✓ 所有文档都成功更新")
        else:
            print(f"⚠️  有 {errors} 个文档更新失败，请检查")

    except Exception as e:
        print(f"生产环境清理过程中发生错误: {e}")
        return

    # 验证清理结果
    print(f"\n验证生产环境清理结果...")

    try:
        # 获取清理后的样本
        final_sample = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 100}
        )

        final_all_fields = set()
        for doc in final_sample["hits"]["hits"]:
            final_all_fields.update(doc["_source"].keys())

        final_nonstandard = final_all_fields - STANDARD_FIELDS

        print(f"验证样本文档数: {len(final_sample['hits']['hits'])}")
        print(f"最终字段数: {len(final_all_fields)}")
        print(f"剩余非标准字段数: {len(final_nonstandard)}")

        if final_nonstandard:
            print("剩余非标准字段:")
            for field in sorted(final_nonstandard):
                print(f"  - {field}")
            print("⚠️  清理未完全成功，可能需要重新运行")
        else:
            print("🎉 生产环境清理完全成功! 所有文档只包含标准字段")

    except Exception as e:
        print(f"验证结果时发生错误: {e}")


def main():
    """主函数"""
    print("=" * 70)
    print("生产环境 Elasticsearch 索引字段清理工具")
    print("=" * 70)

    print(f"标准字段数量: {len(STANDARD_FIELDS)}")

    # 显示将要使用的配置
    es_host = os.getenv("ES_HOST", "http://**********:9200")
    index_name = os.getenv(
        "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
    )

    print(f"生产环境配置:")
    print(f"  ES主机: {es_host}")
    print(f"  目标索引: {index_name}")

    # 执行清理
    cleanup_production_index()


if __name__ == "__main__":
    main()
