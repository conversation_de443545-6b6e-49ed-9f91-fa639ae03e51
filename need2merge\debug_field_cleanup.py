#!/usr/bin/env python3
"""
调试字段清理问题 - 检查映射和文档的差异
"""

from elasticsearch import Elasticsearch

STANDARD_FIELDS = {
    # 业务数据字段（40个）
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    # 源数据元数据字段（6个）
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    # 附件相关字段（8个）
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    # 系统字段（1个）
    "insert_time",
}


def debug_cleanup():
    """调试清理问题"""
    # 连接测试环境
    try:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=30,
        )
    except TypeError:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )

    index_name = "markersweb_attachment_analysis_alias"

    print("调试字段清理问题")
    print("=" * 60)

    try:
        # 1. 检查索引映射中的字段
        print("1. 检查索引映射中的字段:")
        mapping = es.indices.get_mapping(index=index_name)

        mapping_fields = set()
        for index, mapping_data in mapping.items():
            properties = mapping_data.get("mappings", {}).get("properties", {})
            mapping_fields.update(properties.keys())

        mapping_nonstandard = mapping_fields - STANDARD_FIELDS
        print(f"   映射中总字段数: {len(mapping_fields)}")
        print(f"   映射中非标准字段数: {len(mapping_nonstandard)}")

        if mapping_nonstandard:
            print("   映射中的非标准字段:")
            for field in sorted(mapping_nonstandard):
                print(f"     - {field}")

        # 2. 检查实际文档中的字段
        print(f"\n2. 检查实际文档中的字段:")
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10}
        )

        doc_fields = set()
        for doc in sample_response["hits"]["hits"]:
            doc_fields.update(doc["_source"].keys())

        doc_nonstandard = doc_fields - STANDARD_FIELDS
        print(f"   文档中总字段数: {len(doc_fields)}")
        print(f"   文档中非标准字段数: {len(doc_nonstandard)}")

        if doc_nonstandard:
            print("   文档中的非标准字段:")
            for field in sorted(doc_nonstandard):
                print(f"     - {field}")

        # 3. 比较映射和文档字段的差异
        print(f"\n3. 映射与文档字段差异分析:")
        only_in_mapping = mapping_nonstandard - doc_nonstandard
        only_in_docs = doc_nonstandard - mapping_nonstandard
        in_both = mapping_nonstandard & doc_nonstandard

        print(f"   只在映射中存在的非标准字段: {len(only_in_mapping)}")
        if only_in_mapping:
            for field in sorted(only_in_mapping):
                print(f"     - {field}")

        print(f"   只在文档中存在的非标准字段: {len(only_in_docs)}")
        if only_in_docs:
            for field in sorted(only_in_docs):
                print(f"     - {field}")

        print(f"   映射和文档都存在的非标准字段: {len(in_both)}")
        if in_both:
            for field in sorted(in_both):
                print(f"     - {field}")

        # 4. 检查具体文档的字段分布
        print(f"\n4. 检查文档字段分布:")
        field_count = {}
        for doc in sample_response["hits"]["hits"]:
            for field in doc["_source"].keys():
                if field not in STANDARD_FIELDS:
                    field_count[field] = field_count.get(field, 0) + 1

        if field_count:
            print("   非标准字段在样本文档中的出现次数:")
            for field, count in sorted(field_count.items()):
                print(
                    f"     {field}: {count}/{len(sample_response['hits']['hits'])} 文档"
                )

        # 5. 提供解决建议
        print(f"\n5. 问题分析和建议:")
        if only_in_mapping and not doc_nonstandard:
            print("   ✓ 文档已清理完成，但映射中仍有字段定义")
            print("   建议: 需要重建索引或更新映射来完全清理")
        elif doc_nonstandard:
            print("   ✗ 文档中仍有非标准字段")
            print("   建议: 需要重新运行清理脚本")
        elif not mapping_nonstandard and not doc_nonstandard:
            print("   ✓ 映射和文档都已清理完成")
        else:
            print("   需要进一步分析具体情况")

    except Exception as e:
        print(f"调试失败: {e}")


if __name__ == "__main__":
    debug_cleanup()
