#!/usr/bin/env python3
"""
安全执行chn_ylcg索引去重操作的示例脚本

该脚本演示了如何安全地执行去重操作：
1. 先运行测试分析
2. 执行试运行模式
3. 确认后执行实际去重
"""

import sys
import subprocess
from dotenv import load_dotenv

from es_deal import init_es_client
from utils.log_cfg import log


def run_command(command: str, description: str) -> bool:
    """
    执行命令并返回是否成功
    
    Args:
        command: 要执行的命令
        description: 命令描述
    
    Returns:
        bool: 是否执行成功
    """
    try:
        log.info(f"正在执行: {description}")
        log.info(f"命令: {command}")
        
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            log.info(f"✓ {description} 执行成功")
            if result.stdout:
                log.info("输出:")
                for line in result.stdout.strip().split('\n'):
                    log.info(f"  {line}")
            return True
        else:
            log.error(f"✗ {description} 执行失败")
            if result.stderr:
                log.error("错误信息:")
                for line in result.stderr.strip().split('\n'):
                    log.error(f"  {line}")
            return False
            
    except Exception as e:
        log.error(f"执行命令失败: {e}")
        return False


def confirm_action(message: str) -> bool:
    """
    确认用户操作
    
    Args:
        message: 确认消息
    
    Returns:
        bool: 用户是否确认
    """
    while True:
        response = input(f"{message} (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("请输入 y 或 n")


def check_elasticsearch_connection():
    """检查Elasticsearch连接"""
    try:
        log.info("检查Elasticsearch连接...")
        es = init_es_client()
        
        # 检查索引是否存在
        index_name = "chn_ylcg"
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            return False
        
        # 获取索引基本信息
        stats = es.indices.stats(index=index_name)
        doc_count = stats['indices'][index_name]['total']['docs']['count']
        size_bytes = stats['indices'][index_name]['total']['store']['size_in_bytes']
        size_mb = size_bytes / (1024 * 1024)
        
        log.info(f"✓ 索引 {index_name} 连接成功")
        log.info(f"  文档数量: {doc_count:,}")
        log.info(f"  索引大小: {size_mb:.2f} MB")
        
        return True
        
    except Exception as e:
        log.error(f"Elasticsearch连接失败: {e}")
        return False


def main():
    """主函数"""
    try:
        # 加载环境变量
        load_dotenv()
        
        log.info("=" * 80)
        log.info("chn_ylcg索引去重操作 - 安全执行流程")
        log.info("=" * 80)
        
        # 步骤1: 检查Elasticsearch连接
        log.info("\n步骤1: 检查Elasticsearch连接")
        log.info("-" * 40)
        
        if not check_elasticsearch_connection():
            log.error("Elasticsearch连接失败，终止操作")
            sys.exit(1)
        
        # 步骤2: 运行测试分析
        log.info("\n步骤2: 运行测试分析")
        log.info("-" * 40)
        
        if not confirm_action("是否运行测试分析以了解数据情况？"):
            log.info("跳过测试分析")
        else:
            success = run_command(
                "python test_deduplicate_chn_ylcg.py",
                "测试分析"
            )
            if not success:
                if not confirm_action("测试分析失败，是否继续？"):
                    sys.exit(1)
        
        # 步骤3: 执行试运行
        log.info("\n步骤3: 执行试运行模式")
        log.info("-" * 40)
        
        if not confirm_action("是否执行试运行模式以预览去重操作？"):
            log.info("跳过试运行")
        else:
            success = run_command(
                "python deduplicate_chn_ylcg.py --dry-run",
                "试运行模式"
            )
            if not success:
                log.error("试运行失败，建议检查问题后再执行")
                sys.exit(1)
        
        # 步骤4: 确认执行实际去重
        log.info("\n步骤4: 执行实际去重操作")
        log.info("-" * 40)
        
        log.warning("⚠️  注意：接下来将执行实际的删除操作！")
        log.warning("⚠️  请确保已经:")
        log.warning("   1. 查看了试运行的结果")
        log.warning("   2. 确认去重逻辑符合预期")
        log.warning("   3. 做好了数据备份")
        
        if not confirm_action("确认执行实际去重操作？"):
            log.info("用户取消操作")
            sys.exit(0)
        
        # 再次确认
        if not confirm_action("最后确认：真的要执行删除操作吗？"):
            log.info("用户取消操作")
            sys.exit(0)
        
        # 执行实际去重
        success = run_command(
            "python deduplicate_chn_ylcg.py",
            "实际去重操作"
        )
        
        if success:
            log.info("\n✓ 去重操作完成！")
            log.info("建议:")
            log.info("1. 检查生成的备份文件")
            log.info("2. 验证索引数据的完整性")
            log.info("3. 运行应用程序测试")
        else:
            log.error("\n✗ 去重操作失败！")
            log.error("建议:")
            log.error("1. 检查错误日志")
            log.error("2. 验证Elasticsearch状态")
            log.error("3. 如有必要，从备份恢复数据")
        
        # 步骤5: 验证结果
        log.info("\n步骤5: 验证去重结果")
        log.info("-" * 40)
        
        if confirm_action("是否运行验证测试？"):
            run_command(
                "python test_deduplicate_chn_ylcg.py",
                "验证测试"
            )
        
        log.info("\n" + "=" * 80)
        log.info("去重操作流程完成")
        log.info("=" * 80)
        
    except KeyboardInterrupt:
        log.info("\n用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
