import os
import re
from es_deal import init_es_client, search_documents, insert_document
from dotenv import load_dotenv


def remove_ext(filename: str) -> str:
    """
    去除文件名中的扩展名（只去掉最后一个.及其后内容）
    """
    if not filename:
        return filename
    return re.sub(r"\.[^.\\/]+$", "", filename)


def process_appendix_info(appendix_info: list) -> list:
    """
    去除appendix_info中每个dict的text字段的扩展名
    """
    if not isinstance(appendix_info, list):
        return appendix_info
    new_list = []
    for item in appendix_info:
        if not isinstance(item, dict):
            new_list.append(item)
            continue
        new_item = item.copy()
        text = new_item.get("text")
        if isinstance(text, str):
            new_item["text"] = remove_ext(text)
        new_list.append(new_item)
    return new_list


def main():
    load_dotenv()
    es = init_es_client()
    es_index = (
        os.getenv("ES_INDEX_ANALYSIS_ALIAS") or "markersweb_attachment_analysis_alias"
    )

    # 批量scroll遍历所有文档
    query = {"query": {"match_all": {}}}
    result = es.search(index=es_index, body=query, scroll="5m", size=1000)
    scroll_id = result.get("_scroll_id")
    total = result["hits"]["total"]["value"]
    print(f"总文档数: {total}")

    processed = 0
    updated = 0

    while True:
        hits = result["hits"]["hits"]
        if not hits:
            break
        for hit in hits:
            doc_id = hit["_id"]
            doc = hit["_source"]
            appendix_info = doc.get("appendix_info")
            if not appendix_info:
                processed += 1
                continue
            new_appendix_info = process_appendix_info(appendix_info)
            if new_appendix_info != appendix_info:
                doc["appendix_info"] = new_appendix_info
                insert_document(es, es_index, doc_id, doc)
                updated += 1
            processed += 1
            if processed % 100 == 0:
                print(f"已处理: {processed}，已更新: {updated}")
        # scroll下一页
        if not scroll_id:
            break
        result = es.scroll(scroll_id=scroll_id, scroll="5m")
        scroll_id = result.get("_scroll_id")
        if not result["hits"]["hits"]:
            break
    print(f"处理完成，总处理: {processed}，总更新: {updated}")


if __name__ == "__main__":
    main()
