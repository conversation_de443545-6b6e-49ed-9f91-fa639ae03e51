#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查 source_title 字段的当前映射配置

该脚本用于检查 markersweb_attachment_analysis_alias 索引中 source_title 字段的映射配置，
确认是否已经包含 keyword 子字段。
"""

import os
import json
from dotenv import load_dotenv
from es_deal import init_es_client
from utils.log_cfg import log


def check_source_title_mapping():
    """检查 source_title 字段的映射配置"""
    
    # 加载环境变量
    load_dotenv()
    
    # 初始化ES客户端
    es = init_es_client()
    
    # 别名名称
    alias_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
    
    try:
        print("=" * 80)
        print(f"检查索引 {alias_name} 中 source_title 字段的映射配置")
        print("=" * 80)
        
        # 1. 检查别名是否存在
        if not es.indices.exists(index=alias_name):
            print(f"✗ 索引/别名 {alias_name} 不存在")
            return False
        
        # 2. 获取别名指向的实际索引
        alias_info = es.indices.get_alias(name=alias_name)
        actual_indices = list(alias_info.keys())
        
        print(f"别名 {alias_name} 指向的索引:")
        for idx in actual_indices:
            print(f"  - {idx}")
        
        # 使用第一个索引进行检查
        target_index = actual_indices[0]
        
        # 3. 获取索引映射
        mapping_response = es.indices.get_mapping(index=target_index)
        mapping = mapping_response[target_index]["mappings"]
        
        # 4. 检查 source_title 字段配置
        properties = mapping.get("properties", {})
        source_title_config = properties.get("source_title")
        
        print(f"\n当前 source_title 字段配置:")
        print("-" * 50)
        
        if source_title_config is None:
            print("✗ source_title 字段不存在于映射中")
            return False
        
        print(json.dumps(source_title_config, indent=2, ensure_ascii=False))
        
        # 5. 检查是否有 keyword 子字段
        print(f"\n字段类型分析:")
        print("-" * 50)
        
        field_type = source_title_config.get("type", "未知")
        print(f"主字段类型: {field_type}")
        
        fields = source_title_config.get("fields", {})
        if fields:
            print(f"子字段:")
            for field_name, field_config in fields.items():
                sub_type = field_config.get("type", "未知")
                print(f"  - {field_name}: {sub_type}")
                
                if field_name == "keyword" and sub_type == "keyword":
                    print(f"    ✓ keyword 子字段配置正确")
                    ignore_above = field_config.get("ignore_above")
                    if ignore_above:
                        print(f"    ✓ ignore_above: {ignore_above}")
        else:
            print("✗ 没有子字段配置")
        
        # 6. 总结检查结果
        print(f"\n检查结果:")
        print("-" * 50)
        
        has_keyword_subfield = "keyword" in fields and fields["keyword"].get("type") == "keyword"
        
        if field_type == "text" and has_keyword_subfield:
            print("✓ source_title 字段配置正确")
            print("  - 主字段类型为 text，支持全文搜索")
            print("  - 包含 keyword 子字段，支持精准匹配、聚合和排序")
            print("\n可用的查询方式:")
            print("  - source_title (全文搜索)")
            print("  - source_title.keyword (精准匹配)")
            return True
        elif field_type == "text" and not has_keyword_subfield:
            print("⚠ source_title 字段配置不完整")
            print("  - 主字段类型为 text，支持全文搜索")
            print("  - 缺少 keyword 子字段，无法进行精准匹配")
            print("\n建议运行 update_source_title_mapping.py 添加 keyword 子字段")
            return False
        else:
            print("✗ source_title 字段配置异常")
            print(f"  - 当前类型: {field_type}")
            print("  - 建议重新配置字段映射")
            return False
        
    except Exception as e:
        print(f"检查失败: {e}")
        return False


def test_query_capabilities(es, alias_name):
    """测试查询能力"""
    
    print(f"\n测试查询能力:")
    print("-" * 50)
    
    try:
        # 测试全文搜索
        match_query = {
            "query": {"match": {"source_title": "测试"}},
            "size": 1
        }
        
        response = es.search(index=alias_name, body=match_query)
        print(f"✓ 全文搜索 (source_title) 可用")
        
    except Exception as e:
        print(f"✗ 全文搜索测试失败: {e}")
    
    try:
        # 测试精准匹配
        term_query = {
            "query": {"term": {"source_title.keyword": "测试标题"}},
            "size": 1
        }
        
        response = es.search(index=alias_name, body=term_query)
        print(f"✓ 精准匹配 (source_title.keyword) 可用")
        
    except Exception as e:
        print(f"✗ 精准匹配测试失败: {e}")
    
    try:
        # 测试聚合
        agg_query = {
            "aggs": {
                "title_terms": {
                    "terms": {"field": "source_title.keyword", "size": 1}
                }
            },
            "size": 0
        }
        
        response = es.search(index=alias_name, body=agg_query)
        print(f"✓ 聚合查询 (source_title.keyword) 可用")
        
    except Exception as e:
        print(f"✗ 聚合查询测试失败: {e}")


if __name__ == "__main__":
    load_dotenv()
    es = init_es_client()
    alias_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
    
    # 检查映射配置
    mapping_ok = check_source_title_mapping()
    
    # 测试查询能力
    test_query_capabilities(es, alias_name)
    
    print("\n" + "=" * 80)
    
    if mapping_ok:
        print("✓ source_title 字段配置检查通过")
        print("\n你现在可以使用:")
        print("  - python source_title_query_examples.py  # 查看查询示例")
    else:
        print("⚠ source_title 字段需要优化")
        print("\n建议执行:")
        print("  - python update_source_title_mapping.py  # 添加 keyword 子字段")
    
    print("=" * 80)
