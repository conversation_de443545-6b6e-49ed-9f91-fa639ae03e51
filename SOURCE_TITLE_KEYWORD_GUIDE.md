# source_title 字段 Keyword 子字段最佳实践指南

## 概述

本指南介绍如何为 `markersweb_attachment_analysis_alias` 索引的 `source_title` 字段添加 `keyword` 子字段，以实现精准匹配功能。

## 背景

在 Elasticsearch 中，`text` 字段会被分析器处理（分词、小写、词干提取等），无法进行精准的字符串匹配。通过添加 `keyword` 子字段，我们可以同时支持：

- **全文搜索** (使用 `source_title`)：分词匹配，适合模糊搜索
- **精准匹配** (使用 `source_title.keyword`)：完整字符串匹配，适合精确查询、聚合和排序

## 文件说明

### 1. `check_source_title_mapping.py`
**功能**: 检查当前 `source_title` 字段的映射配置
```bash
python check_source_title_mapping.py
```

**输出示例**:
```
✓ source_title 字段配置正确
  - 主字段类型为 text，支持全文搜索
  - 包含 keyword 子字段，支持精准匹配、聚合和排序
```

### 2. `update_source_title_mapping.py`
**功能**: 为 `source_title` 字段添加 `keyword` 子字段
```bash
python update_source_title_mapping.py
```

**执行流程**:
1. 检查当前索引配置
2. 创建新索引（添加 keyword 子字段）
3. 重新索引数据
4. 更新别名指向
5. 验证新配置

### 3. `source_title_query_examples.py`
**功能**: 演示各种查询方式的使用示例
```bash
python source_title_query_examples.py
```

## 使用流程

### 步骤 1: 检查当前配置
```bash
python check_source_title_mapping.py
```

如果输出显示缺少 keyword 子字段，继续下一步。

### 步骤 2: 添加 keyword 子字段
```bash
python update_source_title_mapping.py
```

**注意事项**:
- 该操作会创建新索引并重新索引数据
- 确保有足够的磁盘空间
- 建议在低峰期执行
- 执行时间取决于数据量大小

### 步骤 3: 验证和测试
```bash
python source_title_query_examples.py
```

## 查询示例

### 1. 全文搜索 (分词匹配)
```json
{
  "query": {
    "match": {
      "source_title": "医疗设备采购"
    }
  }
}
```
**特点**: 会匹配包含 "医疗"、"设备"、"采购" 任意词汇的文档

### 2. 短语匹配
```json
{
  "query": {
    "match_phrase": {
      "source_title": "医疗设备采购"
    }
  }
}
```
**特点**: 匹配包含完整短语 "医疗设备采购" 的文档

### 3. 精准匹配
```json
{
  "query": {
    "term": {
      "source_title.keyword": "浙江省人民医院关于医疗设备采购项目招标公告"
    }
  }
}
```
**特点**: 只匹配完全相同的标题

### 4. 前缀匹配
```json
{
  "query": {
    "prefix": {
      "source_title.keyword": "浙江省"
    }
  }
}
```
**特点**: 匹配以 "浙江省" 开头的标题

### 5. 通配符匹配
```json
{
  "query": {
    "wildcard": {
      "source_title.keyword": "*医院*采购*"
    }
  }
}
```
**特点**: 匹配包含 "医院" 和 "采购" 的标题（顺序相关）

### 6. 聚合统计
```json
{
  "aggs": {
    "title_stats": {
      "terms": {
        "field": "source_title.keyword",
        "size": 20
      }
    }
  },
  "size": 0
}
```
**特点**: 按标题分组统计文档数量

### 7. 复合查询
```json
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "source_title": "医疗设备"
          }
        }
      ],
      "filter": [
        {
          "prefix": {
            "source_title.keyword": "浙江"
          }
        }
      ]
    }
  }
}
```
**特点**: 结合全文搜索和精准匹配

## 性能考虑

### keyword 子字段的优势
- **精准匹配**: 不受分析器影响
- **聚合性能**: 聚合操作更高效
- **排序性能**: 排序操作更快
- **内存使用**: 相对较少的内存占用

### 注意事项
- **存储空间**: 会增加一定的存储空间（存储原始字符串）
- **索引时间**: 略微增加索引时间
- **字符串长度**: 建议设置 `ignore_above` 参数避免过长字符串

## 故障排除

### 1. 索引不存在
```
✗ 索引/别名 markersweb_attachment_analysis_alias 不存在
```
**解决**: 检查 `.env` 文件中的 `ES_INDEX_ANALYSIS_ALIAS` 配置

### 2. 权限不足
```
创建索引失败: security_exception
```
**解决**: 确保 ES 用户有创建索引和重新索引的权限

### 3. 磁盘空间不足
```
创建新索引失败: disk space
```
**解决**: 清理磁盘空间或增加存储容量

### 4. 重新索引超时
```
重新索引操作超时
```
**解决**: 增加超时时间或分批处理数据

## 最佳实践

1. **备份数据**: 执行映射更新前备份重要数据
2. **测试环境**: 先在测试环境验证流程
3. **监控资源**: 监控磁盘空间和内存使用
4. **分批处理**: 对于大量数据，考虑分批重新索引
5. **版本控制**: 保留旧索引一段时间以备回滚

## 相关文档

- [Elasticsearch Multi-fields 官方文档](https://www.elastic.co/guide/en/elasticsearch/reference/current/multi-fields.html)
- [Elasticsearch Text vs Keyword 字段类型](https://www.elastic.co/guide/en/elasticsearch/reference/current/mapping-types.html)
- [Elasticsearch Reindex API](https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-reindex.html)
