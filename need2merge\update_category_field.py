#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新 chn_ylcg 索引中的 category 字段
将值 1 更新为 001，将值 4 更新为 004
"""

import os
from dotenv import load_dotenv
from elasticsearch import Elasticsearch
import logging
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("update_category_field.log"),
    ],
)
log = logging.getLogger(__name__)


def init_es_client() -> Elasticsearch:
    """初始化并返回Elasticsearch客户端"""
    try:
        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_host = os.getenv("ES_HOST")
        es_user = os.getenv("ES_USER")
        es_password = os.getenv("ES_PASSWORD")

        es = Elasticsearch(
            hosts=[es_host], basic_auth=(es_user, es_password), verify_certs=False
        )
        log.info(f"ES客户端初始化成功，连接到: {es_host}")
        return es
    except Exception as e:
        raise Exception(f"初始化ES客户端失败: {e}")


def update_category_field(
    es: Elasticsearch, index_name: str, batch_size: int = 1000, dry_run: bool = False
) -> dict:
    """
    更新索引中的 category 字段：
    - 将值 1 更新为 001
    - 将值 4 更新为 004

    Args:
        es (Elasticsearch): ES客户端实例
        index_name (str): 索引名称
        batch_size (int): 批处理大小，默认1000
        dry_run (bool): 是否为试运行模式，True时只统计不实际更新

    Returns:
        dict: 包含处理统计信息的字典
    """
    try:
        log.info(f"开始处理索引 {index_name} 中的 category 字段")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 统计信息
        stats = {
            "total_found": 0,
            "total_processed": 0,
            "category_1_to_001": 0,
            "category_4_to_004": 0,
            "total_errors": 0,
        }

        # 构建查询条件：筛选 category 为 1 或 4 的文档
        query = {
            "query": {
                "bool": {
                    "should": [
                        {"term": {"category": "1"}},
                        {"term": {"category": 1}},
                        {"term": {"category": "4"}},
                        {"term": {"category": 4}},
                    ]
                }
            },
            "size": batch_size,
            "_source": ["_id", "category"],  # 只返回需要的字段
        }

        # 使用scroll API处理大量数据
        scroll_response = es.search(
            index=index_name, body=query, scroll="5m"  # 保持scroll上下文5分钟
        )

        scroll_id = scroll_response.get("_scroll_id")
        total_hits = scroll_response["hits"]["total"]["value"]
        stats["total_found"] = total_hits

        log.info(f"找到 {total_hits} 个需要更新 category 字段的文档")

        if total_hits == 0:
            log.info("没有找到需要处理的文档")
            return stats

        # 创建进度条
        progress_bar = tqdm(total=total_hits, desc="处理进度")

        if dry_run:
            log.info("试运行模式：只统计，不实际更新数据")
            # 在试运行模式下，统计有多少文档需要更新
            while True:
                hits = scroll_response["hits"]["hits"]
                if not hits:
                    break

                for hit in hits:
                    doc_id = hit["_id"]
                    source = hit.get("_source", {})
                    category = source.get("category")

                    # 检查是否需要更新
                    if category == "1" or category == 1:
                        stats["category_1_to_001"] += 1
                    elif category == "4" or category == 4:
                        stats["category_4_to_004"] += 1

                    stats["total_processed"] += 1
                    progress_bar.update(1)

                # 获取下一批数据
                scroll_response = es.scroll(scroll_id=scroll_id, scroll="5m")

            log.info(
                f"试运行统计: 总共找到 {stats['total_found']} 个文档，"
                f"需要将 category=1 更新为 001 的文档数: {stats['category_1_to_001']}，"
                f"需要将 category=4 更新为 004 的文档数: {stats['category_4_to_004']}"
            )

        else:
            # 实际更新模式
            log.info("开始实际更新数据...")

            while True:
                hits = scroll_response["hits"]["hits"]
                if not hits:
                    break

                # 准备批量更新操作
                bulk_body = []

                for hit in hits:
                    doc_id = hit["_id"]
                    source = hit.get("_source", {})
                    category = source.get("category")

                    # 构建更新文档
                    update_doc = {}

                    if category == "1" or category == 1:
                        update_doc["category"] = "001"
                        stats["category_1_to_001"] += 1
                        # 添加到批量操作
                        bulk_body.append(
                            {"update": {"_index": index_name, "_id": doc_id}}
                        )
                        bulk_body.append({"doc": update_doc})
                    elif category == "4" or category == 4:
                        update_doc["category"] = "004"
                        stats["category_4_to_004"] += 1
                        # 添加到批量操作
                        bulk_body.append(
                            {"update": {"_index": index_name, "_id": doc_id}}
                        )
                        bulk_body.append({"doc": update_doc})

                    stats["total_processed"] += 1
                    progress_bar.update(1)

                # 执行批量更新
                if bulk_body:
                    try:
                        bulk_response = es.bulk(body=bulk_body, refresh=True)
                        if bulk_response.get("errors"):
                            for item in bulk_response.get("items", []):
                                if "error" in item.get("update", {}):
                                    stats["total_errors"] += 1
                                    log.error(
                                        f"更新文档失败: {item['update']['error']}"
                                    )
                    except Exception as e:
                        stats["total_errors"] += len(bulk_body) // 2
                        log.error(f"批量更新失败: {e}")

                # 获取下一批数据
                scroll_response = es.scroll(scroll_id=scroll_id, scroll="5m")

            log.info(
                f"更新完成: 总共处理 {stats['total_processed']} 个文档，"
                f"将 category=1 更新为 001 的文档数: {stats['category_1_to_001']}，"
                f"将 category=4 更新为 004 的文档数: {stats['category_4_to_004']}，"
                f"失败数: {stats['total_errors']}"
            )

        # 关闭进度条
        progress_bar.close()

        # 清理scroll上下文
        if scroll_id:
            es.clear_scroll(scroll_id=scroll_id)

        return stats

    except Exception as e:
        log.error(f"更新 category 字段失败: {e}")
        raise


def main():
    """主函数"""
    try:
        # 初始化ES客户端
        es = init_es_client()

        # 从环境变量获取索引名称
        index_name = os.getenv("ES_INDEX_LINKS", "chn_ylcg")

        # 询问是否为试运行模式
        dry_run_input = input("是否为试运行模式？(y/n，默认为y): ").strip().lower()
        dry_run = dry_run_input != "n"

        # 执行更新
        stats = update_category_field(es, index_name, dry_run=dry_run)

        # 打印统计信息
        log.info("处理完成，统计信息:")
        for key, value in stats.items():
            log.info(f"{key}: {value}")

    except Exception as e:
        log.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()
