#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版 Elasticsearch 增量数据同步脚本

修复问题：
1. 确保真正的增量同步，避免重复数据
2. 添加同步状态记录
3. 改进错误处理和日志记录
"""

import sys
import json
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional

import requests
import schedule
from requests.auth import HTTPBasicAuth
import urllib3

# 禁用 InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 定义UTC+8时区
UTC_PLUS_8 = timezone(timedelta(hours=8))

# --- 配置信息 ---
CONFIG = {
    # 源 ES 集群配置
    "SRC_ES_HOSTS": [
        "http://**********:9200",
        "http://**********:9200",
        "http://**********:9200",
    ],
    "SRC_ES_USER": "elastic",
    "SRC_ES_PASS": "W8DOwJ2xs4mBV4BcNBNi",
    # 目标 ES 集群配置
    "DST_ES_HOST": "http://***********:9200",
    "DST_ES_USER": "elastic",
    "DST_ES_PASS": "elastic",
    # 需要同步的索引列表及其对应的时间戳字段
    "INDICES_TO_SYNC": {
        "chn_ylcg": "create_time",
        "markersweb_attachment_analysis_alias": "insert_time",
    },
    # 同步的时间范围（单位：小时）
    "SYNC_RANGE_HOURS": 24,
    # 批处理大小
    "BATCH_SIZE": 1000,
    # 请求超时时间（秒）
    "TIMEOUT": 120,
    # 失败重试次数
    "MAX_RETRIES": 3,
    # 日志文件配置
    "LOG_DIR": "./logs",
    "LOG_FILE": "sync_es_fixed.log",
    # 同步状态记录文件
    "SYNC_STATE_FILE": "./sync_state.json",
}


# --- 日志配置 ---
def setup_logging(log_dir: str, log_file: str):
    """配置日志记录"""
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)

    log_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")

    # 文件处理器
    file_handler = logging.FileHandler(log_path / log_file, encoding="utf-8")
    file_handler.setFormatter(log_formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_formatter)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)


class SyncStateManager:
    """同步状态管理器"""

    def __init__(self, state_file: str):
        self.state_file = Path(state_file)
        self.state = self._load_state()

    def _load_state(self) -> dict:
        """加载同步状态"""
        if self.state_file.exists():
            try:
                with open(self.state_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logging.warning(f"加载同步状态失败: {e}")
        return {}

    def _save_state(self):
        """保存同步状态"""
        try:
            with open(self.state_file, "w", encoding="utf-8") as f:
                json.dump(self.state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存同步状态失败: {e}")

    def get_last_sync_time(self, index_name: str) -> Optional[datetime]:
        """获取索引的最后同步时间"""
        if index_name in self.state:
            try:
                time_str = self.state[index_name].get("last_sync_time")
                if time_str:
                    return datetime.fromisoformat(time_str)
            except Exception as e:
                logging.warning(f"解析最后同步时间失败: {e}")
        return None

    def update_sync_time(self, index_name: str, sync_time: datetime, synced_count: int):
        """更新索引的同步时间"""
        if index_name not in self.state:
            self.state[index_name] = {}

        self.state[index_name].update(
            {
                "last_sync_time": sync_time.isoformat(),
                "last_synced_count": synced_count,
                "last_update": datetime.now(UTC_PLUS_8).isoformat(),
            }
        )
        self._save_state()


class FixedIncrementalESSync:
    """修复版 ES 增量同步器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.src_hosts = config["SRC_ES_HOSTS"]
        self.src_auth = HTTPBasicAuth(config["SRC_ES_USER"], config["SRC_ES_PASS"])
        self.dst_host = config["DST_ES_HOST"]
        self.dst_auth = HTTPBasicAuth(config["DST_ES_USER"], config["DST_ES_PASS"])
        self.indices_config = config["INDICES_TO_SYNC"]
        self.sync_range_hours = config["SYNC_RANGE_HOURS"]
        self.batch_size = config["BATCH_SIZE"]
        self.timeout = config["TIMEOUT"]
        self.max_retries = config["MAX_RETRIES"]
        self.active_src_host = None

        # 初始化状态管理器
        self.state_manager = SyncStateManager(config["SYNC_STATE_FILE"])

        self.stats = {
            "total_synced": 0,
            "total_created": 0,
            "total_updated": 0,
            "indices_synced": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None,
        }

    def _get_available_src_host(self) -> Optional[str]:
        """获取一个可用的源 ES 主机"""
        for host in self.src_hosts:
            try:
                response = requests.get(
                    f"{host}/_cluster/health",
                    auth=self.src_auth,
                    timeout=10,
                    verify=False,
                )
                if response.status_code == 200 and response.json().get("status") in [
                    "green",
                    "yellow",
                ]:
                    logging.info(f"✓ 源 ES 主机可用: {host}")
                    return host
            except requests.RequestException as e:
                logging.warning(f"✗ 源 ES 主机 {host} 不可用: {e}")
        return None

    def _test_connections(self) -> bool:
        """测试与源和目标 ES 的连接"""
        logging.info("--- 正在测试 ES 连接 ---")
        self.active_src_host = self._get_available_src_host()
        if not self.active_src_host:
            logging.error("❌ 所有源 ES 主机都不可用。")
            return False

        try:
            response = requests.get(
                f"{self.dst_host}/_cluster/health",
                auth=self.dst_auth,
                timeout=10,
                verify=False,
            )
            if response.status_code == 200 and response.json().get("status") in [
                "green",
                "yellow",
            ]:
                logging.info(f"✓ 目标 ES 主机可用: {self.dst_host}")
                return True
            else:
                logging.error(f"❌ 目标 ES 主机连接失败: {response.text}")
                return False
        except requests.RequestException as e:
            logging.error(f"❌ 目标 ES 主机 {self.dst_host} 不可用: {e}")
            return False

    def _check_document_exists(self, index_name: str, doc_id: str) -> bool:
        """检查文档是否已存在于目标环境"""
        try:
            response = requests.head(
                f"{self.dst_host}/{index_name}/_doc/{doc_id}",
                auth=self.dst_auth,
                timeout=10,
                verify=False,
            )
            return response.status_code == 200
        except:
            return False

    def _sync_index(
        self,
        index_name: str,
        timestamp_field: str,
        start_time_dt: Optional[datetime] = None,
        end_time_dt: Optional[datetime] = None,
        force_full_sync: bool = False,
    ) -> bool:
        """同步单个索引"""
        synced_count = 0
        created_count = 0
        updated_count = 0
        skipped_count = 0

        # 确定同步时间范围
        if start_time_dt and end_time_dt:
            # 使用传入的自定义时间
            pass
        elif force_full_sync:
            # 全量同步：从很早的时间开始
            start_time_dt = datetime(2020, 1, 1, tzinfo=UTC_PLUS_8)
            end_time_dt = datetime.now(UTC_PLUS_8)
        else:
            # 增量同步：使用上次同步时间或默认时间范围
            last_sync_time = self.state_manager.get_last_sync_time(index_name)
            if last_sync_time:
                start_time_dt = last_sync_time
                logging.info(f"使用上次同步时间: {start_time_dt.isoformat()}")
            else:
                end_time_dt = datetime.now(UTC_PLUS_8)
                start_time_dt = end_time_dt - timedelta(hours=self.sync_range_hours)
                logging.info("首次同步，使用默认时间范围")

            if not end_time_dt:
                end_time_dt = datetime.now(UTC_PLUS_8)

        # 将时间转换为毫秒级时间戳
        start_epoch_ms = int(start_time_dt.timestamp() * 1000)
        end_epoch_ms = int(end_time_dt.timestamp() * 1000)

        logging.info(
            f"同步时间范围 (UTC+8): {start_time_dt.isoformat()} TO {end_time_dt.isoformat()}"
        )

        try:
            # 检查索引和字段
            sample_query = {
                "query": {"match_all": {}},
                "size": 1,
                "_source": [timestamp_field],
            }

            sample_response = requests.post(
                f"{self.active_src_host}/{index_name}/_search",
                auth=self.src_auth,
                json=sample_query,
                timeout=self.timeout,
                verify=False,
            )

            if sample_response.status_code != 200:
                logging.error(f"无法访问源索引 {index_name}")
                return False

            sample_data = sample_response.json()
            total_in_index = (
                sample_data.get("hits", {}).get("total", {}).get("value", 0)
            )
            logging.info(f"源索引 {index_name} 总文档数: {total_in_index:,}")

            if total_in_index == 0:
                logging.info("源索引为空，跳过同步")
                return True

            sample_hits = sample_data.get("hits", {}).get("hits", [])
            if not sample_hits:
                logging.warning("无法获取样本文档")
                return True

            sample_timestamp = sample_hits[0].get("_source", {}).get(timestamp_field)
            logging.info(f"样本时间戳: {sample_timestamp}")

            # 构建查询
            if isinstance(sample_timestamp, str):
                if len(sample_timestamp) == 10 and sample_timestamp.count("-") == 2:
                    # 日期格式
                    start_str = start_time_dt.strftime("%Y-%m-%d")
                    end_str = end_time_dt.strftime("%Y-%m-%d")
                else:
                    # 日期时间格式
                    start_str = start_time_dt.strftime("%Y-%m-%d %H:%M:%S")
                    end_str = end_time_dt.strftime("%Y-%m-%d %H:%M:%S")

                query = {
                    "query": {
                        "bool": {
                            "should": [
                                {
                                    "range": {
                                        f"{timestamp_field}.keyword": {
                                            "gte": start_str,
                                            "lt": end_str,
                                        }
                                    }
                                },
                                {
                                    "range": {
                                        timestamp_field: {
                                            "gte": start_str,
                                            "lt": end_str,
                                        }
                                    }
                                },
                            ],
                            "minimum_should_match": 1,
                        }
                    },
                    "size": self.batch_size,
                    "_source": True,
                }
            else:
                # 时间戳格式
                query = {
                    "query": {
                        "range": {
                            timestamp_field: {
                                "gte": start_epoch_ms,
                                "lt": end_epoch_ms,
                                "format": "epoch_millis",
                            }
                        }
                    },
                    "size": self.batch_size,
                    "_source": True,
                }

            # 执行滚动查询
            scroll_url = f"{self.active_src_host}/{index_name}/_search?scroll=5m"
            response = requests.post(
                scroll_url,
                auth=self.src_auth,
                json=query,
                timeout=self.timeout,
                verify=False,
            )
            response.raise_for_status()

            scroll_data = response.json()
            scroll_id = scroll_data.get("_scroll_id")
            hits = scroll_data.get("hits", {}).get("hits", [])
            total_docs = scroll_data.get("hits", {}).get("total", {}).get("value", 0)

            if total_docs == 0:
                logging.info("在指定时间范围内没有需要同步的文档")
                return True

            logging.info(f"发现 {total_docs:,} 个需要同步的文档")

            while hits:
                bulk_data = []
                batch_stats = {"created": 0, "updated": 0, "skipped": 0}

                for hit in hits:
                    doc_id = hit["_id"]
                    source = hit["_source"]

                    # 检查文档是否已存在（仅在非强制模式下）
                    if not force_full_sync:
                        exists = self._check_document_exists(index_name, doc_id)
                        if exists:
                            # 文档已存在，使用更新操作
                            action = {"update": {"_index": index_name, "_id": doc_id}}
                            data = {"doc": source, "doc_as_upsert": False}
                            batch_stats["updated"] += 1
                        else:
                            # 文档不存在，使用创建操作
                            action = {"create": {"_index": index_name, "_id": doc_id}}
                            data = source
                            batch_stats["created"] += 1
                    else:
                        # 强制模式，使用 upsert
                        action = {"update": {"_index": index_name, "_id": doc_id}}
                        data = {"doc": source, "doc_as_upsert": True}

                    bulk_data.append(json.dumps(action))
                    bulk_data.append(json.dumps(data))

                # 执行批量操作
                if bulk_data:
                    success, batch_result = self._bulk_upsert(bulk_data)
                    if not success:
                        logging.error(f"❌ 批量写入失败，索引 {index_name} 的同步中止")
                        self.stats["errors"] += 1
                        return False

                    # 更新统计
                    created_count += batch_result.get("created", 0)
                    updated_count += batch_result.get("updated", 0)

                synced_count += len(hits)
                self.stats["total_synced"] += len(hits)

                logging.info(
                    f"✓ 已同步 {synced_count:,}/{total_docs:,} 个文档 (创建: {created_count}, 更新: {updated_count})"
                )

                # 继续滚动
                if not scroll_id:
                    break

                scroll_response = requests.post(
                    f"{self.active_src_host}/_search/scroll",
                    auth=self.src_auth,
                    json={"scroll": "5m", "scroll_id": scroll_id},
                    timeout=self.timeout,
                    verify=False,
                )

                if scroll_response.status_code == 200:
                    scroll_data = scroll_response.json()
                    scroll_id = scroll_data.get("_scroll_id")
                    hits = scroll_data.get("hits", {}).get("hits", [])
                else:
                    logging.error(f"❌ 滚动查询失败: {scroll_response.text}")
                    break

            # 清理滚动上下文
            if scroll_id:
                requests.delete(
                    f"{self.active_src_host}/_search/scroll",
                    auth=self.src_auth,
                    json={"scroll_id": [scroll_id]},
                    verify=False,
                )

            # 更新同步状态
            if synced_count > 0:
                self.state_manager.update_sync_time(
                    index_name, end_time_dt, synced_count
                )

            self.stats["total_created"] += created_count
            self.stats["total_updated"] += updated_count

            logging.info(
                f"索引 {index_name} 同步完成: 总计 {synced_count:,} 个文档 (创建: {created_count}, 更新: {updated_count})"
            )
            return True

        except Exception as e:
            logging.error(f"❌ 同步索引 {index_name} 时发生错误: {e}", exc_info=True)
            self.stats["errors"] += 1
            return False

    def _bulk_upsert(self, bulk_data: List[str]) -> tuple[bool, dict]:
        """执行批量操作，返回成功状态和统计信息"""
        if not bulk_data:
            return True, {}

        bulk_body = "\n".join(bulk_data) + "\n"

        for retry in range(self.max_retries):
            try:
                response = requests.post(
                    f"{self.dst_host}/_bulk",
                    auth=self.dst_auth,
                    data=bulk_body,
                    headers={"Content-Type": "application/x-ndjson"},
                    timeout=self.timeout * 2,
                    verify=False,
                )
                response.raise_for_status()

                result = response.json()

                # 统计结果
                stats = {"created": 0, "updated": 0, "errors": 0}

                for item in result.get("items", []):
                    if "create" in item:
                        if item["create"].get("error"):
                            stats["errors"] += 1
                        else:
                            stats["created"] += 1
                    elif "update" in item:
                        if item["update"].get("error"):
                            stats["errors"] += 1
                        else:
                            if item["update"].get("result") == "created":
                                stats["created"] += 1
                            else:
                                stats["updated"] += 1

                if result.get("errors"):
                    logging.warning(
                        f"⚠️ 批量操作部分失败: 成功 {stats['created'] + stats['updated']}, 失败 {stats['errors']}"
                    )
                    # 记录部分错误但继续处理
                    for item in result.get("items", []):
                        for action_type in ["create", "update"]:
                            if action_type in item and item[action_type].get("error"):
                                error_info = item[action_type]["error"]
                                logging.warning(
                                    f"  - ID: {item[action_type]['_id']}, 错误: {error_info.get('reason', '未知')}"
                                )

                return True, stats

            except requests.RequestException as e:
                logging.warning(
                    f"⚠️ 批量写入网络异常 (尝试 {retry + 1}/{self.max_retries}): {e}"
                )
                if retry == self.max_retries - 1:
                    return False, {}
                time.sleep(2**retry)

        return False, {}

    def run_sync(
        self,
        start_time_dt: Optional[datetime] = None,
        end_time_dt: Optional[datetime] = None,
        force_full_sync: bool = False,
    ):
        """执行同步任务"""
        self.stats["start_time"] = time.time()
        sync_type = "全量同步" if force_full_sync else "增量同步"
        logging.info("=" * 60)
        logging.info(
            f"ES {sync_type}任务开始于: {datetime.now(UTC_PLUS_8).strftime('%Y-%m-%d %H:%M:%S')} (UTC+8)"
        )
        logging.info("=" * 60)

        if not self._test_connections():
            logging.error("❌ 连接测试失败，同步任务中止")
            return

        for index_name, timestamp_field in self.indices_config.items():
            logging.info(
                f"\n--- 开始同步索引: {index_name} (时间字段: {timestamp_field}) ---"
            )
            if self._sync_index(
                index_name, timestamp_field, start_time_dt, end_time_dt, force_full_sync
            ):
                logging.info(f"✓ 索引 {index_name} 同步成功")
                self.stats["indices_synced"] += 1
            else:
                logging.warning(f"✗ 索引 {index_name} 同步失败")

        self.stats["end_time"] = time.time()
        self._print_summary()

    def _print_summary(self):
        """打印同步结果摘要"""
        duration = self.stats["end_time"] - self.stats["start_time"]
        summary = [
            "\n" + "=" * 60,
            "同步任务摘要",
            "=" * 60,
            f"总耗时: {duration:.2f} 秒",
            f"同步索引数: {self.stats['indices_synced']}",
            f"同步文档数: {self.stats['total_synced']:,}",
            f"  - 新创建: {self.stats['total_created']:,}",
            f"  - 已更新: {self.stats['total_updated']:,}",
            f"错误数: {self.stats['errors']}",
            "=" * 60,
        ]
        for line in summary:
            logging.info(line)


def main():
    """主函数"""
    setup_logging(CONFIG["LOG_DIR"], CONFIG["LOG_FILE"])
    sync_manager = FixedIncrementalESSync(CONFIG)

    if len(sys.argv) > 1:
        if sys.argv[1] == "diagnose":
            # 运行诊断脚本
            import subprocess

            subprocess.run([sys.executable, "diagnose_es_data.py"])
            return
        elif sys.argv[1] == "full":
            logging.info("--- 执行全量同步 ---")
            sync_manager.run_sync(force_full_sync=True)
        elif sys.argv[1] == "manual":
            logging.info("--- 执行手动同步模式 ---")
            logging.info("手动模式选项:")
            logging.info("1. 增量同步 (默认)")
            logging.info("2. 全量同步")
            logging.info("3. 自定义时间范围")

            try:
                choice = input("请选择同步模式 (1/2/3): ").strip()

                if choice == "2":
                    confirm = (
                        input("确认执行全量同步? 这可能需要很长时间 (y/N): ")
                        .strip()
                        .lower()
                    )
                    if confirm in ["y", "yes"]:
                        sync_manager.run_sync(force_full_sync=True)
                    else:
                        logging.info("用户取消操作")
                elif choice == "3":
                    start_input = input(
                        "请输入开始时间 (YYYY-MM-DD HH:MM:SS): "
                    ).strip()
                    end_input = input("请输入结束时间 (YYYY-MM-DD HH:MM:SS): ").strip()

                    try:
                        start_dt = datetime.strptime(
                            start_input, "%Y-%m-%d %H:%M:%S"
                        ).replace(tzinfo=UTC_PLUS_8)
                        end_dt = datetime.strptime(
                            end_input, "%Y-%m-%d %H:%M:%S"
                        ).replace(tzinfo=UTC_PLUS_8)

                        confirm = (
                            input(f"确认同步时间范围 {start_dt} 到 {end_dt}? (y/N): ")
                            .strip()
                            .lower()
                        )
                        if confirm in ["y", "yes"]:
                            sync_manager.run_sync(start_dt, end_dt)
                        else:
                            logging.info("用户取消操作")
                    except ValueError as e:
                        logging.error(f"时间格式错误: {e}")
                else:
                    # 默认增量同步
                    confirm = input("确认执行增量同步? (y/N): ").strip().lower()
                    if confirm in ["y", "yes"]:
                        sync_manager.run_sync()
                    else:
                        logging.info("用户取消操作")

            except KeyboardInterrupt:
                logging.info("用户中断操作")
        else:
            print("使用说明:")
            print("python sync_es_data_fixed.py          # 增量同步")
            print("python sync_es_data_fixed.py manual   # 手动模式")
            print("python sync_es_data_fixed.py full     # 全量同步")
            print("python sync_es_data_fixed.py diagnose # 运行诊断")
    else:
        # 默认增量同步
        sync_manager.run_sync()


if __name__ == "__main__":
    main()
