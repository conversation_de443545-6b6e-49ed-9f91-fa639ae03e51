#!/usr/bin/env python3
"""
删除剩余的 contract_link 字段
"""

from elasticsearch import Elasticsearch


def remove_contract_link():
    """删除 contract_link 字段"""
    # 连接测试环境
    try:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=60,
        )
    except TypeError:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )

    index_name = "markersweb_attachment_analysis_alias"

    print("删除 contract_link 字段")
    print("=" * 40)

    try:
        # 检查有多少文档包含 contract_link 字段
        count_query = {"query": {"exists": {"field": "contract_link"}}}

        count_response = es.count(index=index_name, body=count_query)
        docs_with_field = count_response["count"]

        print(f"包含 contract_link 字段的文档数: {docs_with_field}")

        if docs_with_field == 0:
            print("✓ 没有文档包含 contract_link 字段")
            return

        # 使用 update_by_query 删除字段
        update_query = {
            "query": {"exists": {"field": "contract_link"}},
            "script": {"source": "ctx._source.remove('contract_link')"},
        }

        print(f"开始删除 {docs_with_field} 个文档中的 contract_link 字段...")

        response = es.update_by_query(
            index=index_name, body=update_query, wait_for_completion=True, refresh=True
        )

        updated = response.get("updated", 0)
        print(f"✓ 成功更新 {updated} 个文档")

        # 验证删除结果
        final_count = es.count(index=index_name, body=count_query)["count"]
        print(f"删除后包含 contract_link 字段的文档数: {final_count}")

        if final_count == 0:
            print("✓ contract_link 字段已完全删除")
        else:
            print(f"✗ 仍有 {final_count} 个文档包含 contract_link 字段")

    except Exception as e:
        print(f"删除失败: {e}")


if __name__ == "__main__":
    remove_contract_link()
