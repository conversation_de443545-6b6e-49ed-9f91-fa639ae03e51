#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引映射信息
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_v3")
print(f"使用索引: {index_name}")

try:
    # 获取索引映射
    mapping = es.indices.get_mapping(index=index_name)
    print("获取索引映射成功")

    # 打印部分映射信息以检查字段名称
    properties = mapping[index_name]["mappings"].get("properties", {})
    print(f"索引中的部分字段: {list(properties.keys())[:10]}")

    # 检查是否存在 source_url 和 source_category 字段
    if "source_url" in properties:
        print("source_url 字段存在")
    else:
        print("source_url 字段不存在，尝试查找类似字段...")
        possible_url_fields = [
            field for field in properties.keys() if "url" in field.lower()
        ]
        print(f"可能的 URL 字段: {possible_url_fields}")

    if "source_category" in properties:
        print("source_category 字段存在")
    else:
        print("source_category 字段不存在，尝试查找类似字段...")
        possible_category_fields = [
            field for field in properties.keys() if "category" in field.lower()
        ]
        print(f"可能的 category 字段: {possible_category_fields}")

    # 尝试多种查询方式
    queries = [
        # 原始查询 - http
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "http://www.xinecai.com"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "http prefix + term",
        },
        # 原始查询 - https
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "https://www.xinecai.com"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "https prefix + term",
        },
        # 尝试使用 wildcard 而不是 prefix - http
        {
            "query": {
                "bool": {
                    "must": [
                        {"wildcard": {"source_url": "http://www.xinecai.com*"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "http wildcard + term",
        },
        # 尝试使用 wildcard 而不是 prefix - https
        {
            "query": {
                "bool": {
                    "must": [
                        {"wildcard": {"source_url": "https://www.xinecai.com*"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "https wildcard + term",
        },
        # 尝试使用 match_phrase_prefix - http
        {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase_prefix": {
                                "source_url": "http://www.xinecai.com"
                            }
                        },
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "http match_phrase_prefix + term",
        },
        # 尝试使用 match_phrase_prefix - https
        {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase_prefix": {
                                "source_url": "https://www.xinecai.com"
                            }
                        },
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "https match_phrase_prefix + term",
        },
        # 尝试使用 match 而不是 term 对 source_category
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "http://www.xinecai.com"}},
                        {"match": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "http prefix + match",
        },
        # 尝试使用 match 而不是 term 对 source_category - https
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "https://www.xinecai.com"}},
                        {"match": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "https prefix + match",
        },
        # 尝试使用数字格式的 4
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "http://www.xinecai.com"}},
                        {"term": {"source_category": 4}},
                    ]
                }
            },
            "size": 0,
            "description": "http prefix + term(数字4)",
        },
        # 尝试使用数字格式的 4 - https
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "https://www.xinecai.com"}},
                        {"term": {"source_category": 4}},
                    ]
                }
            },
            "size": 0,
            "description": "https prefix + term(数字4)",
        },
        # 尝试只查询 source_url - http
        {
            "query": {"prefix": {"source_url": "http://www.xinecai.com"}},
            "size": 0,
            "description": "只查询 http source_url",
        },
        # 尝试只查询 source_url - https
        {
            "query": {"prefix": {"source_url": "https://www.xinecai.com"}},
            "size": 0,
            "description": "只查询 https source_url",
        },
        # 尝试只查询 source_category
        {
            "query": {"term": {"source_category": "004"}},
            "size": 0,
            "description": "只查询 source_category=004",
        },
        # 尝试使用 xinecai.com 而不是 www.xinecai.com
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "http://xinecai.com"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "http://xinecai.com prefix + term",
        },
        # 尝试使用 xinecai.com 而不是 www.xinecai.com - https
        {
            "query": {
                "bool": {
                    "must": [
                        {"prefix": {"source_url": "https://xinecai.com"}},
                        {"term": {"source_category": "004"}},
                    ]
                }
            },
            "size": 0,
            "description": "https://xinecai.com prefix + term",
        },
    ]

    print("\n尝试不同的查询方式:")
    successful_queries = []

    for i, query in enumerate(queries):
        try:
            description = query.pop("description", f"查询 {i+1}")
            response = es.search(index=index_name, body=query)
            count = response["hits"]["total"]["value"]
            print(f"{description}: 符合条件的文档数量: {count}")

            if count > 0:
                successful_queries.append((description, count, query))

                # 获取一个示例文档
                if len(successful_queries) == 1:  # 只对第一个成功的查询获取示例
                    sample_query = query.copy()
                    sample_query["size"] = 1
                    sample_response = es.search(index=index_name, body=sample_query)
                    if sample_response["hits"]["hits"]:
                        sample_doc = sample_response["hits"]["hits"][0]["_source"]
                        print(
                            f"\n示例文档: {json.dumps({k: v for k, v in list(sample_doc.items())[:10]}, ensure_ascii=False, indent=2)}"
                        )

                        if "source_category" in sample_doc:
                            print(
                                f"source_category 值: {sample_doc['source_category']}, 类型: {type(sample_doc['source_category'])}"
                            )
                        if "source_url" in sample_doc:
                            print(f"source_url 值: {sample_doc['source_url']}")
        except Exception as e:
            print(f"{description} 执行失败: {str(e)}")

    if successful_queries:
        print("\n成功的查询结果:")
        for desc, count, _ in successful_queries:
            print(f"{desc}: {count} 条文档")

        # 使用最佳查询获取更多信息
        best_query = max(successful_queries, key=lambda x: x[1])
        print(f"\n最佳查询: {best_query[0]}, 文档数量: {best_query[1]}")
    else:
        print("\n所有查询都没有找到匹配的文档")

        # 尝试获取一些随机文档来检查字段
        print("\n获取一些随机文档来检查字段:")
        random_query = {"query": {"match_all": {}}, "size": 3}
        try:
            random_response = es.search(index=index_name, body=random_query)
            for hit in random_response["hits"]["hits"]:
                doc = hit["_source"]
                print("\n随机文档部分字段:")
                for field in ["source_url", "source_category"]:
                    if field in doc:
                        print(f"{field}: {doc[field]}")
                    else:
                        print(f"{field} 不存在")

                # 查找可能的替代字段
                url_fields = {k: v for k, v in doc.items() if "url" in k.lower()}
                if url_fields:
                    print(f"可能的 URL 字段: {url_fields}")

                category_fields = {
                    k: v for k, v in doc.items() if "category" in k.lower()
                }
                if category_fields:
                    print(f"可能的 category 字段: {category_fields}")
        except Exception as e:
            print(f"获取随机文档失败: {str(e)}")

except Exception as e:
    print(f"查询执行失败: {str(e)}")
    exit(1)
