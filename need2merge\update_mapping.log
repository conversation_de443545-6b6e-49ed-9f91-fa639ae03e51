2025-07-28 09:28:15,494 - INFO - ============================================================
2025-07-28 09:28:15,494 - INFO - 开始 ES 字段映射更新任务
2025-07-28 09:28:15,495 - INFO - ============================================================
2025-07-28 09:28:15,506 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3
2025-07-28 09:28:15,514 - INFO - 当前 insert_time 字段类型: text
2025-07-28 09:28:15,515 - INFO - 已更新索引 markersweb_attachment_analysis_v3 的 insert_time 字段映射为 date 类型
2025-07-28 09:28:15,686 - INFO - 新索引 markersweb_attachment_analysis_v3_date_20250728_092815 创建成功
2025-07-28 09:28:15,686 - INFO - 开始重建索引: markersweb_attachment_analysis_v3 -> markersweb_attachment_analysis_v3_date_20250728_092815
2025-07-28 09:28:15,913 - ERROR - 重建索引失败: 400 Client Error: Bad Request for url: http://172.18.10.8:9200/_reindex
2025-07-28 09:28:15,914 - ERROR - 重建索引失败
2025-07-28 09:30:54,604 - INFO - ============================================================
2025-07-28 09:30:54,604 - INFO - 开始 ES 字段映射更新任务
2025-07-28 09:30:54,604 - INFO - ============================================================
2025-07-28 09:30:54,622 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3
2025-07-28 09:30:54,632 - INFO - 当前 insert_time 字段类型: text
2025-07-28 09:30:54,633 - INFO - 已更新索引 markersweb_attachment_analysis_v3 的 insert_time 字段映射为 date 类型
2025-07-28 09:30:54,633 - INFO - 准备重建索引 markersweb_attachment_analysis_v3，修改 insert_time 字段类型为 date
2025-07-28 09:30:54,648 - INFO - 已更新 insert_time 字段映射为 date 类型
2025-07-28 09:30:54,849 - INFO - 新索引 markersweb_attachment_analysis_v3_date_20250728_093054 创建成功
2025-07-28 09:30:54,944 - ERROR - 重建索引失败: 400 Client Error: Bad Request for url: http://172.18.10.8:9200/_reindex
2025-07-28 09:30:54,945 - ERROR - 重建索引失败
2025-07-28 09:31:37,034 - INFO - ============================================================
2025-07-28 09:31:37,034 - INFO - 开始 ES 字段映射更新任务
2025-07-28 09:31:37,034 - INFO - ============================================================
2025-07-28 09:31:37,048 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3
2025-07-28 09:31:37,060 - INFO - 当前 insert_time 字段类型: text
2025-07-28 09:31:37,060 - INFO - 已更新索引 markersweb_attachment_analysis_v3 的 insert_time 字段映射为 date 类型
2025-07-28 09:31:37,060 - INFO - 准备重建索引 markersweb_attachment_analysis_v3，修改 insert_time 字段类型为 date
2025-07-28 09:31:37,075 - INFO - 已更新 insert_time 字段映射为 date 类型
2025-07-28 09:31:37,212 - INFO - 新索引 markersweb_attachment_analysis_v3_date_20250728_093137 创建成功
2025-07-28 09:31:37,303 - ERROR - Reindex请求失败，状态码: 400
2025-07-28 09:31:37,303 - ERROR - 错误响应: {"error":{"root_cause":[{"type":"script_exception","reason":"compile error","script_stack":["... e 字段\n                    if (ctx._source.insert_ti ...","                             ^---- HERE"],"script":"\n                    // 处理 insert_time 字段\n                    if (ctx._source.insert_time != null && ctx._source.insert_time != '') {\n                        // 如果是字符串格式，保持不变，ES会自动解析为date\n                        // 如果已经是正确格式，也保持不变\n                    } else {\n                        // 如果字段为空，设置为当前时间\n                        ctx._source.insert_time = '2025-07-28 09:30:00';\n                    }\n                ","lang":"painless","position":{"offset":62,"start":37,"end":87}}],"type":"script_exception","reason":"compile error","script_stack":["... e 字段\n                    if (ctx._source.insert_ti ...","                             ^---- HERE"],"script":"\n                    // 处理 insert_time 字段\n                    if (ctx._source.insert_time != null && ctx._source.insert_time != '') {\n                        // 如果是字符串格式，保持不变，ES会自动解析为date\n                        // 如果已经是正确格式，也保持不变\n                    } else {\n                        // 如果字段为空，设置为当前时间\n                        ctx._source.insert_time = '2025-07-28 09:30:00';\n                    }\n                ","lang":"painless","position":{"offset":62,"start":37,"end":87},"caused_by":{"type":"illegal_argument_exception","reason":"extraneous if block"}},"status":400}
2025-07-28 09:31:37,305 - ERROR - 重建索引失败
2025-07-28 09:32:04,009 - INFO - ============================================================
2025-07-28 09:32:04,009 - INFO - 开始 ES 字段映射更新任务
2025-07-28 09:32:04,009 - INFO - ============================================================
2025-07-28 09:32:04,020 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3
2025-07-28 09:32:04,032 - INFO - 当前 insert_time 字段类型: text
2025-07-28 09:32:04,033 - INFO - 已更新索引 markersweb_attachment_analysis_v3 的 insert_time 字段映射为 date 类型
2025-07-28 09:32:04,033 - INFO - 准备重建索引 markersweb_attachment_analysis_v3，修改 insert_time 字段类型为 date
2025-07-28 09:32:04,055 - INFO - 已更新 insert_time 字段映射为 date 类型
2025-07-28 09:32:04,151 - INFO - 新索引 markersweb_attachment_analysis_v3_date_20250728_093204 创建成功
2025-07-28 09:32:05,420 - INFO - 数据reindex到 markersweb_attachment_analysis_v3_date_20250728_093204 完成
2025-07-28 09:32:05,440 - INFO - 数据验证: 原索引 3325 条，新索引 2000 条
2025-07-28 09:32:05,440 - WARNING - 新旧索引数据量不一致，请检查
2025-07-28 09:32:05,478 - INFO - 别名 markersweb_attachment_analysis_alias 已从 markersweb_attachment_analysis_v3 切换到 markersweb_attachment_analysis_v3_date_20250728_093204
2025-07-28 09:32:05,478 - INFO - 别名更新成功
2025-07-28 09:32:13,219 - INFO - 保留旧索引 markersweb_attachment_analysis_v3
2025-07-28 09:32:13,219 - INFO - ============================================================
2025-07-28 09:32:13,220 - INFO - ES 字段映射更新任务完成
2025-07-28 09:32:13,220 - INFO - ============================================================
