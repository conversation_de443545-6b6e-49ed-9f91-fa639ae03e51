#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引名称
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_alias")
print(f"使用索引: {index_name}")

# 定义查询 - 查找prj_name为空且source_url以https://www.xinecai.com开头的文档
query = {
    "query": {
        "bool": {
            "must": [
                {"match_phrase_prefix": {"source_url": "https://www.xinecai.com"}},
                {
                    "bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "prj_name"}}}},
                            {"term": {"prj_name": ""}},
                        ],
                        "minimum_should_match": 1,
                    }
                },
            ]
        }
    },
    "size": 0,  # 只需要计数，不需要返回文档内容
}

try:
    # 执行查询
    response = es.search(index=index_name, body=query)

    # 获取匹配的文档数量
    count = response["hits"]["total"]["value"]
    print(f"符合条件的文档数量: {count}")
    print(f"查询条件: source_url 以 'https://www.xinecai.com' 开头且 prj_name 为空")

    # 获取一些示例文档
    if count > 0:
        sample_query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase_prefix": {
                                "source_url": "https://www.xinecai.com"
                            }
                        },
                        {
                            "bool": {
                                "should": [
                                    {
                                        "bool": {
                                            "must_not": {
                                                "exists": {"field": "prj_name"}
                                            }
                                        }
                                    },
                                    {"term": {"prj_name": ""}},
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                    ]
                }
            },
            "size": 5,  # 获取5个示例文档
            "_source": [
                "source_url",
                "prj_name",
                "release_time",
                "province",
                "city",
                "county",
                "object_name",
            ],
        }

        sample_response = es.search(index=index_name, body=sample_query)
        print(f"\n示例文档 (共 {len(sample_response['hits']['hits'])} 个):")

        for i, hit in enumerate(sample_response["hits"]["hits"]):
            doc = hit["_source"]
            print(f"\n示例 {i+1}:")
            print(f"ID: {hit['_id']}")
            print(f"source_url: {doc.get('source_url', 'N/A')}")

            # 显示prj_name的状态
            if "prj_name" not in doc:
                print("prj_name: 字段不存在")
            else:
                print(f"prj_name: '{doc['prj_name']}'")

            # 打印其他一些可能有用的字段
            interesting_fields = [
                "release_time",
                "province",
                "city",
                "county",
                "object_name",
            ]
            for field in interesting_fields:
                if field in doc:
                    print(f"{field}: {doc[field]}")

except Exception as e:
    print(f"查询执行失败: {str(e)}")
    exit(1)
