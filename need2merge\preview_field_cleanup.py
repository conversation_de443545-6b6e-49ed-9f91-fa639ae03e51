#!/usr/bin/env python3
"""
预览 Elasticsearch 索引字段清理
只显示将要删除的字段，不执行实际删除操作
"""

import json
from elasticsearch import Elasticsearch

# 标准字段定义（与主脚本相同）
STANDARD_FIELDS = {
    # 业务数据字段（40个）
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    # 源数据元数据字段（6个）
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    # 附件相关字段（8个）
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    # 系统字段（1个）
    "insert_time",
}


def preview_cleanup():
    """预览清理操作"""
    # 连接测试环境
    try:
        # 尝试新版本的参数
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=30,
        )
    except TypeError:
        # 兼容旧版本
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )

    index_name = "markersweb_attachment_analysis_alias"

    print(f"预览索引 {index_name} 的字段清理操作")
    print("=" * 60)

    try:
        # 获取索引映射
        mapping = es.indices.get_mapping(index=index_name)

        all_fields = set()
        for index, mapping_data in mapping.items():
            properties = mapping_data.get("mappings", {}).get("properties", {})
            all_fields.update(properties.keys())

        # 分析字段
        nonstandard_fields = all_fields - STANDARD_FIELDS
        standard_fields = all_fields & STANDARD_FIELDS

        print(f"索引中总字段数: {len(all_fields)}")
        print(f"标准字段数: {len(standard_fields)}")
        print(f"非标准字段数: {len(nonstandard_fields)}")

        if nonstandard_fields:
            print(f"\n将要删除的非标准字段 ({len(nonstandard_fields)} 个):")
            for i, field in enumerate(sorted(nonstandard_fields), 1):
                print(f"  {i:2d}. {field}")

        print(f"\n将要保留的标准字段 ({len(standard_fields)} 个):")
        for i, field in enumerate(sorted(standard_fields), 1):
            print(f"  {i:2d}. {field}")

        # 获取文档总数
        count_response = es.count(index=index_name)
        total_docs = count_response["count"]
        print(f"\n索引中总文档数: {total_docs:,}")

        # 获取样本文档
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 3}
        )

        print(f"\n样本文档字段分析:")
        for i, doc in enumerate(sample_response["hits"]["hits"], 1):
            source = doc["_source"]
            doc_nonstandard = set(source.keys()) - STANDARD_FIELDS
            print(
                f"  文档 {i}: 总字段 {len(source)}, 非标准字段 {len(doc_nonstandard)}"
            )
            if doc_nonstandard:
                print(f"    非标准字段: {', '.join(sorted(doc_nonstandard))}")

        if nonstandard_fields:
            print(f"\n⚠️  执行清理后将删除 {len(nonstandard_fields)} 个非标准字段")
            print("   请确认这些字段确实不需要保留")
        else:
            print("\n✓ 没有需要清理的非标准字段")

    except Exception as e:
        print(f"预览失败: {e}")


if __name__ == "__main__":
    preview_cleanup()
