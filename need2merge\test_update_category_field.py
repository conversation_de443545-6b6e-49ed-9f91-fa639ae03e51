#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 update_category_field.py 脚本的功能
"""

import os
from dotenv import load_dotenv
from elasticsearch import Elasticsearch
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
log = logging.getLogger(__name__)


def init_es_client() -> Elasticsearch:
    """初始化并返回Elasticsearch客户端"""
    try:
        # 加载.env文件
        load_dotenv()

        # 从环境变量获取配置
        es_host = os.getenv("ES_HOST")
        es_user = os.getenv("ES_USER")
        es_password = os.getenv("ES_PASSWORD")

        es = Elasticsearch(
            hosts=[es_host], basic_auth=(es_user, es_password), verify_certs=False
        )
        log.info(f"ES客户端初始化成功，连接到: {es_host}")
        return es
    except Exception as e:
        raise Exception(f"初始化ES客户端失败: {e}")


def count_documents_by_category(es: Elasticsearch, index_name: str) -> dict:
    """
    统计不同 category 值的文档数量

    Args:
        es: ES客户端
        index_name: 索引名称

    Returns:
        dict: 包含各 category 值文档数量的字典
    """
    try:
        # 使用聚合查询统计各 category 值的文档数量
        query = {
            "size": 0,
            "aggs": {
                "category_counts": {
                    "terms": {
                        "field": "category",
                        "size": 100,  # 返回最多100个不同的category值
                    }
                }
            },
        }

        result = es.search(index=index_name, body=query)

        # 提取聚合结果
        buckets = (
            result.get("aggregations", {}).get("category_counts", {}).get("buckets", [])
        )

        # 构建统计结果
        stats = {}
        for bucket in buckets:
            category = bucket.get("key")
            count = bucket.get("doc_count", 0)
            stats[category] = count

        return stats

    except Exception as e:
        log.error(f"统计 category 字段失败: {e}")
        raise


def main():
    """主函数"""
    try:
        # 初始化ES客户端
        es = init_es_client()

        # 从环境变量获取索引名称
        index_name = os.getenv("ES_INDEX_LINKS", "chn_ylcg")

        # 统计更新前的 category 分布
        log.info(f"统计索引 {index_name} 中 category 字段的分布情况...")
        stats = count_documents_by_category(es, index_name)

        # 打印统计结果
        log.info("category 字段分布情况:")
        for category, count in sorted(stats.items()):
            log.info(f"category={category}: {count} 个文档")

        # 特别关注的值
        log.info("\n特别关注的值:")
        log.info(f"category=1: {stats.get('1', 0)} 个文档")
        log.info(f"category=4: {stats.get('4', 0)} 个文档")
        log.info(f"category=001: {stats.get('001', 0)} 个文档")
        log.info(f"category=004: {stats.get('004', 0)} 个文档")

    except Exception as e:
        log.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()
