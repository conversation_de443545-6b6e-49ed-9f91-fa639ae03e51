#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试环境 Elasticsearch 字段映射更新脚本

该脚本用于将测试环境 http://***********:9200 的 markersweb_attachment_analysis_alias 索引中的：
1. appendix_info 字段从 text 类型转换为 nested 类型
2. insert_time 字段从 text 类型转换为 date 类型

注意：由于 ES 不支持直接修改已存在字段的类型，需要通过重建索引的方式实现。
"""

import json
import logging
import requests
from datetime import datetime
from requests.auth import HTTPBasicAuth
import urllib3

# 禁用 InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 测试环境 ES 配置
ES_CONFIG = {
    "HOST": "http://***********:9200",
    "USER": "elastic",
    "PASS": "elastic",
    "INDEX_ALIAS": "markersweb_attachment_analysis_alias",
    "TIMEOUT": 120,
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("update_test_env_mapping.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)


def get_current_mapping(host: str, auth: HTTPBasicAuth, index_name: str) -> dict:
    """获取当前索引的映射"""
    try:
        response = requests.get(
            f"{host}/{index_name}/_mapping",
            auth=auth,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logging.error(f"获取索引映射失败: {e}")
        return {}


def rebuild_index_with_new_mapping(
    host: str, auth: HTTPBasicAuth, old_index: str, new_index: str
) -> bool:
    """重建索引，同时修改 appendix_info 和 insert_time 字段类型"""
    try:
        logging.info(f"准备重建索引 {old_index}，修改字段类型")
        logging.info("- appendix_info: text -> nested")
        logging.info("- insert_time: text -> date")

        # 1. 获取原mapping和settings
        response = requests.get(f"{host}/{old_index}/_mapping", auth=auth, verify=False)
        response.raise_for_status()
        mapping_data = response.json()
        mapping = mapping_data[old_index]["mappings"]

        response = requests.get(
            f"{host}/{old_index}/_settings", auth=auth, verify=False
        )
        response.raise_for_status()
        settings_data = response.json()
        settings = settings_data[old_index]["settings"]["index"]

        # 2. 修改字段映射
        if "properties" in mapping:
            # 修改 appendix_info 字段为 nested 类型
            if "appendix_info" in mapping["properties"]:
                mapping["properties"]["appendix_info"] = {
                    "type": "nested",
                    "properties": {
                        "file_ext": {"type": "keyword"},
                        "file_link_key": {"type": "keyword"},
                        "text": {"type": "text"},
                        "url": {"type": "keyword"},
                    },
                }
                logging.info("已更新 appendix_info 字段映射为 nested 类型")

            # 修改 insert_time 字段为 date 类型，支持多种时间格式
            if "insert_time" in mapping["properties"]:
                mapping["properties"]["insert_time"] = {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis||yyyy-MM-dd'T'HH:mm:ss.SSSSSS||yyyy-MM-dd'T'HH:mm:ss||strict_date_optional_time",
                }
                logging.info(
                    "已更新 insert_time 字段映射为 date 类型，支持多种时间格式"
                )

        # 3. 过滤设置
        filtered_settings = {}
        for k, v in settings.items():
            if not k.startswith(("version", "uuid", "creation_date", "provided_name")):
                filtered_settings[k] = v

        # 4. 创建新索引
        body = {
            "settings": filtered_settings,
            "mappings": mapping,
        }

        response = requests.put(
            f"{host}/{new_index}",
            auth=auth,
            json=body,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        logging.info(f"新索引 {new_index} 创建成功")

        # 5. reindex数据，处理两个字段
        reindex_body = {
            "source": {"index": old_index},
            "dest": {"index": new_index},
            "script": {
                "lang": "painless",
                "source": """
                    // 处理 appendix_info 字段
                    if (ctx._source.appendix_info != null) {
                        if (ctx._source.appendix_info instanceof String) {
                            String jsonStr = ctx._source.appendix_info;
                            if (jsonStr.equals("[]") || jsonStr.equals("")) {
                                ctx._source.appendix_info = [];
                            } else {
                                // 对于复杂的JSON字符串，暂时设为空数组
                                ctx._source.appendix_info = [];
                            }
                        }
                    } else {
                        ctx._source.appendix_info = [];
                    }
                    
                    // 处理 insert_time 字段，支持多种时间格式
                    if (ctx._source.insert_time == null || ctx._source.insert_time == '') {
                        // 如果字段为空，设置为当前时间
                        ctx._source.insert_time = '2025-07-28 10:00:00';
                    } else {
                        // 标准化时间格式
                        String timeStr = ctx._source.insert_time.toString();
                        
                        // 如果是ISO格式（包含T），转换为标准格式
                        if (timeStr.contains('T')) {
                            // 移除微秒部分，只保留到秒
                            if (timeStr.contains('.')) {
                                int dotIndex = timeStr.indexOf('.');
                                timeStr = timeStr.substring(0, dotIndex);
                            }
                            // 将T替换为空格
                            timeStr = timeStr.replace('T', ' ');
                        }
                        
                        ctx._source.insert_time = timeStr;
                    }
                """,
            },
        }

        response = requests.post(
            f"{host}/_reindex",
            auth=auth,
            json=reindex_body,
            timeout=ES_CONFIG["TIMEOUT"] * 10,
            verify=False,
        )

        if response.status_code != 200:
            logging.error(f"Reindex请求失败，状态码: {response.status_code}")
            logging.error(f"错误响应: {response.text}")
            return False

        response.raise_for_status()
        result = response.json()
        logging.info(f"数据reindex到 {new_index} 完成")
        return True

    except Exception as e:
        logging.error(f"重建索引失败: {e}")
        if hasattr(e, "response") and e.response:
            logging.error(f"错误详情: {e.response.text}")
        return False


def update_alias(
    host: str, auth: HTTPBasicAuth, old_index: str, new_index: str, alias: str
) -> bool:
    """更新别名指向"""
    alias_body = {
        "actions": [
            {"remove": {"index": old_index, "alias": alias}},
            {"add": {"index": new_index, "alias": alias}},
        ]
    }

    try:
        response = requests.post(
            f"{host}/_aliases",
            auth=auth,
            json=alias_body,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        logging.info(f"别名 {alias} 已从 {old_index} 切换到 {new_index}")
        return True

    except Exception as e:
        logging.error(f"更新别名失败: {e}")
        return False


def main():
    """主函数"""
    host = ES_CONFIG["HOST"]
    auth = HTTPBasicAuth(ES_CONFIG["USER"], ES_CONFIG["PASS"])
    alias = ES_CONFIG["INDEX_ALIAS"]

    logging.info("=" * 60)
    logging.info("开始测试环境 ES 字段映射更新任务")
    logging.info(f"目标环境: {host}")
    logging.info(f"目标索引别名: {alias}")
    logging.info("修改内容:")
    logging.info("1. appendix_info: text -> nested")
    logging.info("2. insert_time: text/date -> date (支持多种格式)")
    logging.info("=" * 60)

    # 1. 获取当前别名指向的索引
    try:
        response = requests.get(f"{host}/_alias/{alias}", auth=auth, verify=False)
        response.raise_for_status()
        alias_info = response.json()

        if not alias_info:
            logging.error(f"别名 {alias} 不存在")
            return

        # 获取当前索引名
        current_indices = list(alias_info.keys())
        if not current_indices:
            logging.error(f"别名 {alias} 没有指向任何索引")
            return

        current_index = current_indices[0]  # 假设别名只指向一个索引
        logging.info(f"当前别名 {alias} 指向索引: {current_index}")

    except Exception as e:
        logging.error(f"获取别名信息失败: {e}")
        return

    # 2. 获取当前映射
    current_mapping = get_current_mapping(host, auth, current_index)
    if not current_mapping:
        logging.error("获取当前映射失败")
        return

    # 3. 检查字段类型
    index_mapping = current_mapping.get(current_index, {})
    properties = index_mapping.get("mappings", {}).get("properties", {})

    appendix_info_mapping = properties.get("appendix_info", {})
    appendix_info_type = appendix_info_mapping.get("type", "unknown")

    insert_time_mapping = properties.get("insert_time", {})
    insert_time_type = insert_time_mapping.get("type", "unknown")

    logging.info(f"当前 appendix_info 字段类型: {appendix_info_type}")
    logging.info(f"当前 insert_time 字段类型: {insert_time_type}")

    # 检查是否需要更新
    need_update = False
    if appendix_info_type != "nested":
        logging.info("appendix_info 字段需要更新为 nested 类型")
        need_update = True
    else:
        logging.info("appendix_info 字段已经是 nested 类型")

    # 检查 insert_time 字段类型和格式
    insert_time_format = insert_time_mapping.get("format", "")
    required_formats = ["yyyy-MM-dd'T'HH:mm:ss.SSSSSS", "strict_date_optional_time"]

    if insert_time_type != "date":
        logging.info("insert_time 字段需要更新为 date 类型")
        need_update = True
    elif not any(fmt in insert_time_format for fmt in required_formats):
        logging.info(f"insert_time 字段是 date 类型，但格式不完整")
        logging.info(f"当前格式: {insert_time_format}")
        logging.info("需要更新为支持多种时间格式")
        need_update = True
    else:
        logging.info("insert_time 字段已经是 date 类型且支持多种格式")

    if not need_update:
        logging.info("所有字段类型都已正确，无需更新")
        return

    # 4. 创建新索引名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_index = f"{current_index}_fixed_{timestamp}"

    logging.info(f"新索引名: {new_index}")

    # 5. 重建索引并转换数据
    if not rebuild_index_with_new_mapping(host, auth, current_index, new_index):
        logging.error("重建索引失败")
        return

    # 6. 验证新索引数据
    try:
        response = requests.get(f"{host}/{new_index}/_count", auth=auth, verify=False)
        response.raise_for_status()
        new_count = response.json()["count"]

        response = requests.get(
            f"{host}/{current_index}/_count", auth=auth, verify=False
        )
        response.raise_for_status()
        old_count = response.json()["count"]

        logging.info(f"数据验证: 原索引 {old_count} 条，新索引 {new_count} 条")

        if new_count != old_count:
            logging.warning("新旧索引数据量不一致，请检查")
            # 但不直接返回，继续执行后续步骤

    except Exception as e:
        logging.error(f"数据验证失败: {e}")

    # 7. 更新别名
    if update_alias(host, auth, current_index, new_index, alias):
        logging.info("别名更新成功")

        logging.info("=" * 60)
        logging.info("索引重建完成！")
        logging.info(f"旧索引: {current_index}")
        logging.info(f"新索引: {new_index}")
        logging.info(f"别名: {alias} 现在指向新索引")
        logging.info("字段类型已更新:")
        logging.info("  - appendix_info: nested")
        logging.info("  - insert_time: date (支持多种时间格式)")
        logging.info("=" * 60)

        # 询问是否删除旧索引
        try:
            delete_confirm = input(
                f"是否删除旧索引 {current_index}? 请输入 'DELETE' 确认删除: "
            ).strip()
            if delete_confirm == "DELETE":
                try:
                    response = requests.delete(
                        f"{host}/{current_index}", auth=auth, verify=False
                    )
                    response.raise_for_status()
                    logging.info(f"旧索引 {current_index} 已删除")
                except Exception as e:
                    logging.error(f"删除旧索引失败: {e}")
            else:
                logging.info(f"保留旧索引 {current_index}")
        except KeyboardInterrupt:
            logging.info("用户中断，保留旧索引")

    else:
        logging.error("别名更新失败")

    logging.info("=" * 60)
    logging.info("测试环境 ES 字段映射更新任务完成")
    logging.info("=" * 60)


if __name__ == "__main__":
    main()
