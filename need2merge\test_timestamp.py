#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from datetime import datetime, timezone

# 测试时间戳转换
test_time_str = "2025-07-25 09:00:00"
test_dt = datetime.strptime(test_time_str, "%Y-%m-%d %H:%M:%S")
test_dt_utc = test_dt.replace(tzinfo=timezone.utc)

print(f"原始时间字符串: {test_time_str}")
print(f"解析后的 datetime: {test_dt}")
print(f"UTC datetime: {test_dt_utc}")
print(f"时间戳 (秒): {test_dt_utc.timestamp()}")
print(f"时间戳 (毫秒): {int(test_dt_utc.timestamp() * 1000)}")

# 验证反向转换
timestamp_ms = int(test_dt_utc.timestamp() * 1000)
back_to_dt = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
print(f"反向转换验证: {back_to_dt.isoformat()}")
