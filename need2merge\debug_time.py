from datetime import datetime, timezone

# 模拟你的输入
test_time = "2025-07-25 09:00:00"
dt = datetime.strptime(test_time, "%Y-%m-%d %H:%M:%S")
dt_utc = dt.replace(tzinfo=timezone.utc)

print(f"输入时间: {test_time}")
print(f"解析后: {dt}")
print(f"UTC时间: {dt_utc}")
print(f"时间戳(秒): {dt_utc.timestamp()}")
print(f"时间戳(毫秒): {int(dt_utc.timestamp() * 1000)}")

# 检查当前时间
now = datetime.now(timezone.utc)
print(f"当前UTC时间: {now}")
print(f"当前时间戳(毫秒): {int(now.timestamp() * 1000)}")

# 验证你看到的错误时间戳
wrong_timestamp = 1753405200000
correct_time = datetime.fromtimestamp(wrong_timestamp / 1000, tz=timezone.utc)
print(f"错误时间戳 {wrong_timestamp} 对应时间: {correct_time}")
