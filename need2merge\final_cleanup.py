#!/usr/bin/env python3
"""
最终清理 - 删除所有剩余的非标准字段
"""

from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk

# 标准字段列表
STANDARD_FIELDS = {
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    "insert_time",
}


def setup_es():
    """设置ES连接"""
    try:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=60,
        )
    except TypeError:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )
    return es


def final_cleanup():
    """最终清理所有非标准字段"""
    es = setup_es()
    index_name = "markersweb_attachment_analysis_alias"

    print("最终清理所有非标准字段")
    print("=" * 50)

    # 强制刷新索引
    es.indices.refresh(index=index_name)

    try:
        # 1. 先分析当前状态
        print("1. 分析当前文档字段...")
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10000}
        )

        all_doc_fields = set()
        nonstandard_fields_count = {}

        for doc in sample_response["hits"]["hits"]:
            doc_fields = set(doc["_source"].keys())
            all_doc_fields.update(doc_fields)

            # 统计非标准字段
            for field in doc_fields:
                if field not in STANDARD_FIELDS:
                    nonstandard_fields_count[field] = (
                        nonstandard_fields_count.get(field, 0) + 1
                    )

        nonstandard_fields = all_doc_fields - STANDARD_FIELDS

        print(f"   样本文档数: {len(sample_response['hits']['hits'])}")
        print(f"   文档中总字段数: {len(all_doc_fields)}")
        print(f"   非标准字段数: {len(nonstandard_fields)}")

        if nonstandard_fields:
            print("   非标准字段分布:")
            for field in sorted(nonstandard_fields):
                count = nonstandard_fields_count.get(field, 0)
                print(
                    f"     - {field}: {count}/{len(sample_response['hits']['hits'])} 文档"
                )
        else:
            print("   ✓ 没有发现非标准字段")
            return

        # 2. 确认清理操作
        print(f"\n2. 准备清理 {len(nonstandard_fields)} 个非标准字段")
        confirm = input("确认要删除所有非标准字段吗? (yes/no): ")
        if confirm.lower() != "yes":
            print("操作已取消")
            return

        # 3. 执行清理
        print("\n3. 开始清理...")

        processed_docs = 0
        updated_docs = 0
        errors = 0
        batch_size = 10000

        # 扫描所有文档
        query = {"query": {"match_all": {}}}
        actions = []

        for doc in scan(es, query=query, index=index_name, size=batch_size):
            processed_docs += 1
            doc_id = doc["_id"]
            doc_source = doc["_source"]

            # 找出需要删除的非标准字段
            fields_to_remove = []
            for field in doc_source.keys():
                if field not in STANDARD_FIELDS:
                    fields_to_remove.append(field)

            if fields_to_remove:
                # 构建只包含标准字段的新文档
                cleaned_source = {}
                for field, value in doc_source.items():
                    if field in STANDARD_FIELDS:
                        cleaned_source[field] = value

                # 使用 index 操作替换整个文档
                action = {
                    "_op_type": "index",
                    "_index": index_name,
                    "_id": doc_id,
                    "_source": cleaned_source,
                }

                actions.append(action)
                updated_docs += 1

                if len(actions) >= batch_size:
                    # 执行批量更新
                    try:
                        success_count, failed_items = bulk(
                            es, actions, request_timeout=60
                        )
                        if failed_items:
                            errors += len(failed_items)
                            print(f"批量更新中有 {len(failed_items)} 个失败")
                    except Exception as e:
                        print(f"批量更新失败: {e}")
                        errors += len(actions)

                    actions = []

                    # 显示进度
                    if processed_docs % 1000 == 0:
                        print(
                            f"进度: 已处理 {processed_docs} 文档, 已更新 {updated_docs} 文档"
                        )

        # 处理剩余的操作
        if actions:
            try:
                success_count, failed_items = bulk(es, actions, request_timeout=60)
                if failed_items:
                    errors += len(failed_items)
                    print(f"最后批次更新中有 {len(failed_items)} 个失败")
            except Exception as e:
                print(f"最后批次更新失败: {e}")
                errors += len(actions)

        # 刷新索引
        es.indices.refresh(index=index_name)

        print(f"\n4. 清理完成!")
        print(f"   总处理文档数: {processed_docs}")
        print(f"   需要更新的文档数: {updated_docs}")
        print(f"   失败的文档数: {errors}")

        # 5. 验证结果
        print(f"\n5. 验证清理结果...")

        final_sample = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 50}
        )

        final_all_fields = set()
        for doc in final_sample["hits"]["hits"]:
            final_all_fields.update(doc["_source"].keys())

        final_nonstandard = final_all_fields - STANDARD_FIELDS

        print(f"   验证样本文档数: {len(final_sample['hits']['hits'])}")
        print(f"   最终字段数: {len(final_all_fields)}")
        print(f"   剩余非标准字段数: {len(final_nonstandard)}")

        if final_nonstandard:
            print("   剩余非标准字段:")
            for field in sorted(final_nonstandard):
                print(f"     - {field}")
            print("   ✗ 清理未完全成功")
        else:
            print("   ✓ 清理完全成功! 所有文档只包含标准字段")

    except Exception as e:
        print(f"清理过程中发生错误: {e}")


if __name__ == "__main__":
    final_cleanup()
