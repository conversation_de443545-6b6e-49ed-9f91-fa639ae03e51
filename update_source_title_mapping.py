#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
为 markersweb_attachment_analysis_alias 索引的 source_title 字段添加 keyword 子字段

该脚本用于将 source_title 字段从单纯的 text 类型升级为支持 multi-fields 的配置，
添加 keyword 子字段以支持精准匹配、聚合和排序操作。

注意：由于 ES 不支持直接修改已存在字段的类型，需要通过重建索引的方式实现。
"""

import os
import json
import time
from datetime import datetime
from dotenv import load_dotenv
from es_deal import init_es_client, add_alias, remove_alias
from utils.log_cfg import log


def get_current_mapping(es, index_name):
    """获取当前索引的映射"""
    try:
        response = es.indices.get_mapping(index=index_name)
        return response[index_name]["mappings"]
    except Exception as e:
        raise Exception(f"获取索引映射失败: {e}")


def get_current_settings(es, index_name):
    """获取当前索引的设置"""
    try:
        response = es.indices.get_settings(index=index_name)
        settings = response[index_name]["settings"]["index"]
        
        # 过滤掉不能复制的设置
        filtered_settings = {}
        for k, v in settings.items():
            if not k.startswith(("version", "uuid", "creation_date", "provided_name")):
                filtered_settings[k] = v
        
        return filtered_settings
    except Exception as e:
        raise Exception(f"获取索引设置失败: {e}")


def create_updated_mapping(current_mapping):
    """创建更新后的映射，为 source_title 字段添加 keyword 子字段"""
    updated_mapping = current_mapping.copy()
    
    # 确保 properties 存在
    if "properties" not in updated_mapping:
        updated_mapping["properties"] = {}
    
    # 检查 source_title 字段当前配置
    current_source_title = updated_mapping["properties"].get("source_title", {})
    
    log.info(f"当前 source_title 字段配置: {current_source_title}")
    
    # 更新 source_title 字段配置，添加 keyword 子字段
    updated_mapping["properties"]["source_title"] = {
        "type": "text",
        "fields": {
            "keyword": {
                "type": "keyword",
                "ignore_above": 256  # 忽略超过256字符的字符串，防止内存溢出
            }
        },
        "meta": {"description": "源文档标题"}
    }
    
    log.info("已更新 source_title 字段配置，添加 keyword 子字段")
    return updated_mapping


def create_new_index(es, new_index_name, mapping, settings):
    """创建新索引"""
    try:
        body = {
            "settings": settings,
            "mappings": mapping
        }
        
        es.indices.create(index=new_index_name, body=body)
        log.info(f"新索引 {new_index_name} 创建成功")
        
    except Exception as e:
        raise Exception(f"创建新索引失败: {e}")


def reindex_data(es, source_index, target_index):
    """重新索引数据"""
    try:
        log.info(f"开始从 {source_index} 重新索引数据到 {target_index}")
        
        # 使用 reindex API
        body = {
            "source": {"index": source_index},
            "dest": {"index": target_index}
        }
        
        response = es.reindex(body=body, wait_for_completion=True, timeout="30m")
        
        if response.get("timed_out"):
            raise Exception("重新索引操作超时")
        
        total = response.get("total", 0)
        created = response.get("created", 0)
        
        log.info(f"重新索引完成: 总计 {total} 个文档，创建 {created} 个文档")
        
        return True
        
    except Exception as e:
        raise Exception(f"重新索引失败: {e}")


def update_source_title_mapping():
    """为 source_title 字段添加 keyword 子字段的主函数"""
    
    # 加载环境变量
    load_dotenv()
    
    # 初始化ES客户端
    es = init_es_client()
    
    # 别名名称
    alias_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
    
    try:
        log.info("开始为 source_title 字段添加 keyword 子字段")
        
        # 1. 获取别名指向的实际索引
        alias_info = es.indices.get_alias(name=alias_name)
        if not alias_info:
            log.error(f"别名 {alias_name} 不存在")
            return False
        
        # 获取当前索引名
        current_index = list(alias_info.keys())[0]
        log.info(f"当前别名 {alias_name} 指向索引: {current_index}")
        
        # 2. 检查当前 source_title 字段配置
        current_mapping = get_current_mapping(es, current_index)
        current_source_title = current_mapping.get("properties", {}).get("source_title", {})
        
        # 检查是否已经有 keyword 子字段
        if "fields" in current_source_title and "keyword" in current_source_title["fields"]:
            log.info("source_title 字段已经包含 keyword 子字段，无需更新")
            return True
        
        # 3. 生成新索引名（添加时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_index = f"{current_index}_keyword_{timestamp}"
        
        log.info(f"准备为 source_title 字段添加 keyword 子字段")
        log.info(f"新索引名: {new_index}")
        
        # 4. 获取当前设置和映射
        current_settings = get_current_settings(es, current_index)
        updated_mapping = create_updated_mapping(current_mapping)
        
        # 5. 创建新索引
        create_new_index(es, new_index, updated_mapping, current_settings)
        
        # 6. 重新索引数据
        if not reindex_data(es, current_index, new_index):
            log.error("重新索引失败")
            return False
        
        # 7. 验证新索引
        new_mapping = get_current_mapping(es, new_index)
        new_source_title = new_mapping.get("properties", {}).get("source_title", {})
        
        if "fields" in new_source_title and "keyword" in new_source_title["fields"]:
            log.info("✓ 验证成功: source_title 字段已包含 keyword 子字段")
        else:
            log.error("✗ 验证失败: source_title 字段缺少 keyword 子字段")
            return False
        
        # 8. 更新别名
        log.info(f"移除别名 {alias_name} 从索引 {current_index}")
        remove_alias(es, current_index, alias_name)
        
        log.info(f"为新索引 {new_index} 添加别名 {alias_name}")
        add_alias(es, new_index, alias_name)
        
        log.info("source_title 字段 keyword 子字段添加完成！")
        log.info(f"现在可以使用以下方式进行查询:")
        log.info(f"  - 全文搜索: source_title")
        log.info(f"  - 精准匹配: source_title.keyword")
        
        return True
        
    except Exception as e:
        log.error(f"更新 source_title 字段映射失败: {e}")
        return False


def test_keyword_functionality(es, alias_name):
    """测试 keyword 子字段功能"""
    try:
        log.info("测试 source_title.keyword 字段功能...")
        
        # 测试精准匹配查询
        test_query = {
            "query": {
                "term": {
                    "source_title.keyword": "测试标题"
                }
            },
            "size": 1
        }
        
        response = es.search(index=alias_name, body=test_query)
        log.info(f"精准匹配查询测试完成，返回 {response['hits']['total']['value']} 个结果")
        
        # 测试聚合功能
        agg_query = {
            "aggs": {
                "title_terms": {
                    "terms": {
                        "field": "source_title.keyword",
                        "size": 5
                    }
                }
            },
            "size": 0
        }
        
        response = es.search(index=alias_name, body=agg_query)
        buckets = response.get("aggregations", {}).get("title_terms", {}).get("buckets", [])
        log.info(f"聚合查询测试完成，返回 {len(buckets)} 个聚合桶")
        
        log.info("✓ keyword 子字段功能测试通过")
        return True
        
    except Exception as e:
        log.error(f"keyword 子字段功能测试失败: {e}")
        return False


if __name__ == "__main__":
    success = update_source_title_mapping()
    
    if success:
        # 加载环境变量
        load_dotenv()
        es = init_es_client()
        alias_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
        
        # 测试新功能
        test_keyword_functionality(es, alias_name)
        
        print("\n" + "="*60)
        print("✓ source_title 字段 keyword 子字段添加成功！")
        print("\n现在你可以使用以下查询方式:")
        print("1. 全文搜索 (分词匹配):")
        print('   GET /markersweb_attachment_analysis_alias/_search')
        print('   {')
        print('     "query": {')
        print('       "match": {')
        print('         "source_title": "招标公告"')
        print('       }')
        print('     }')
        print('   }')
        print("\n2. 精准匹配 (完整字符串匹配):")
        print('   GET /markersweb_attachment_analysis_alias/_search')
        print('   {')
        print('     "query": {')
        print('       "term": {')
        print('         "source_title.keyword": "某某医院关于医疗设备采购项目招标公告"')
        print('       }')
        print('     }')
        print('   }')
        print("\n3. 聚合统计:")
        print('   GET /markersweb_attachment_analysis_alias/_search')
        print('   {')
        print('     "aggs": {')
        print('       "title_stats": {')
        print('         "terms": {')
        print('           "field": "source_title.keyword",')
        print('           "size": 10')
        print('         }')
        print('       }')
        print('     },')
        print('     "size": 0')
        print('   }')
        print("="*60)
    else:
        print("\n✗ source_title 字段 keyword 子字段添加失败")
        exit(1)
