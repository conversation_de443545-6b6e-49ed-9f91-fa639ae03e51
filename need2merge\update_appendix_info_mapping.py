#!/usr/bin/env python3
"""
修改 markersweb_attachment_analysis_alias 索引中的 appendix_info 字段类型从 text 改为 nested
"""

import os
from es_deal import init_es_client, add_alias, remove_alias
from dotenv import load_dotenv
from utils.log_cfg import log
from datetime import datetime


def rebuild_index_for_appendix_info(es, old_index: str, new_index: str):
    """专门为 appendix_info 字段重建索引"""
    try:
        log.info(f"准备重建索引 {old_index}，修改 appendix_info 字段类型为 nested")

        # 1. 获取原mapping和settings
        mapping = es.indices.get_mapping(index=old_index)[old_index]["mappings"]
        settings = es.indices.get_settings(index=old_index)[old_index]["settings"][
            "index"
        ]

        # 2. 修改 appendix_info 字段映射
        if "properties" in mapping and "appendix_info" in mapping["properties"]:
            mapping["properties"].pop("appendix_info")

        mapping["properties"]["appendix_info"] = {
            "type": "nested",
            "properties": {
                "file_ext": {"type": "keyword"},
                "file_link_key": {"type": "keyword"},
                "text": {"type": "text"},
                "url": {"type": "keyword"},
            },
        }

        # 3. 过滤设置
        filtered_settings = {}
        for k, v in settings.items():
            if not k.startswith(("version", "uuid", "creation_date", "provided_name")):
                filtered_settings[k] = v

        # 4. 创建新索引
        body = {
            "settings": filtered_settings,
            "mappings": mapping,
        }
        es.indices.create(index=new_index, body=body)
        log.info(f"新索引 {new_index} 创建成功")

        # 5. reindex数据，只处理 appendix_info 字段
        reindex_body = {
            "source": {"index": old_index},
            "dest": {"index": new_index},
            "script": {
                "lang": "painless",
                "source": """
                    // 只处理 appendix_info 字段
                    if (ctx._source.appendix_info != null) {
                        if (ctx._source.appendix_info instanceof String) {
                            String jsonStr = ctx._source.appendix_info;
                            if (jsonStr.equals("[]") || jsonStr.equals("")) {
                                ctx._source.appendix_info = [];
                            } else {
                                // 对于复杂的JSON字符串，暂时设为空数组
                                ctx._source.appendix_info = [];
                            }
                        }
                    } else {
                        ctx._source.appendix_info = [];
                    }
                """,
            },
        }

        es.reindex(body=reindex_body, wait_for_completion=True, request_timeout=3600)
        log.info(f"数据reindex到 {new_index} 完成")

    except Exception as e:
        raise Exception(f"重建索引失败: {e}")


def update_appendix_info_mapping():
    """更新 appendix_info 字段映射为 nested 类型"""

    # 加载环境变量
    load_dotenv()

    # 初始化ES客户端
    es = init_es_client()

    # 别名名称
    alias_name = "markersweb_attachment_analysis_alias"

    try:
        # 1. 获取别名指向的实际索引
        alias_info = es.indices.get_alias(name=alias_name)
        if not alias_info:
            log.error(f"别名 {alias_name} 不存在")
            return

        # 获取当前索引名
        current_index = list(alias_info.keys())[0]
        log.info(f"当前别名 {alias_name} 指向索引: {current_index}")

        # 2. 生成新索引名（添加时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_index = f"{current_index}_nested_{timestamp}"

        log.info(f"准备将 appendix_info 字段从 text 类型改为 nested 类型")
        log.info(f"新索引名: {new_index}")

        # 3. 重建索引
        rebuild_index_for_appendix_info(es, current_index, new_index)

        # 4. 移除旧别名
        log.info(f"移除别名 {alias_name} 从索引 {current_index}")
        remove_alias(es, current_index, alias_name)

        # 5. 为新索引添加别名
        log.info(f"为新索引 {new_index} 添加别名 {alias_name}")
        add_alias(es, new_index, alias_name)

        log.info("=" * 60)
        log.info("索引重建完成！")
        log.info(f"旧索引: {current_index}")
        log.info(f"新索引: {new_index}")
        log.info(f"别名: {alias_name} 现在指向新索引")
        log.info("请验证数据无误后手动删除旧索引")
        log.info("=" * 60)

    except Exception as e:
        log.error(f"更新索引映射失败: {e}")
        raise


if __name__ == "__main__":
    update_appendix_info_mapping()
