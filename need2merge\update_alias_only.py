#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
仅更新别名指向
"""

import requests
from requests.auth import HTTPBasicAuth
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置
HOST = "http://**********:9200"
AUTH = HTTPBasicAuth("elastic", "W8DOwJ2xs4mBV4BcNBNi")

OLD_INDEX = "markersweb_attachment_analysis_v3_nested_20250725_131633"
NEW_INDEX = (
    "markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105241"
)
ALIAS = "markersweb_attachment_analysis_alias"


def main():
    print("=" * 60)
    print("更新别名指向")
    print("=" * 60)

    # 验证索引存在
    old_resp = requests.head(f"{HOST}/{OLD_INDEX}", auth=AUTH, verify=False)
    new_resp = requests.head(f"{HOST}/{NEW_INDEX}", auth=AUTH, verify=False)

    if old_resp.status_code != 200:
        print(f"❌ 旧索引不存在: {OLD_INDEX}")
        return

    if new_resp.status_code != 200:
        print(f"❌ 新索引不存在: {NEW_INDEX}")
        return

    print(f"✓ 旧索引存在: {OLD_INDEX}")
    print(f"✓ 新索引存在: {NEW_INDEX}")

    # 检查数据量
    old_count_resp = requests.get(f"{HOST}/{OLD_INDEX}/_count", auth=AUTH, verify=False)
    new_count_resp = requests.get(f"{HOST}/{NEW_INDEX}/_count", auth=AUTH, verify=False)

    if old_count_resp.status_code == 200 and new_count_resp.status_code == 200:
        old_count = old_count_resp.json()["count"]
        new_count = new_count_resp.json()["count"]
        print(f"数据量对比: 旧索引 {old_count:,} 条, 新索引 {new_count:,} 条")

        if old_count != new_count:
            print("⚠️  数据量不一致")
            confirm = input("是否仍要继续更新别名? (yes/no): ")
            if confirm.lower() != "yes":
                print("取消操作")
                return

    # 检查新索引的字段类型
    mapping_resp = requests.get(f"{HOST}/{NEW_INDEX}/_mapping", auth=AUTH, verify=False)
    if mapping_resp.status_code == 200:
        mapping_data = mapping_resp.json()
        properties = mapping_data[NEW_INDEX]["mappings"]["properties"]
        insert_time_type = properties.get("insert_time", {}).get("type", "unknown")
        print(f"✓ 新索引 insert_time 字段类型: {insert_time_type}")

        if insert_time_type != "date":
            print("❌ 新索引的 insert_time 字段类型不是 date")
            return

    print(f"\n准备更新别名:")
    print(f"  别名: {ALIAS}")
    print(f"  从: {OLD_INDEX}")
    print(f"  到: {NEW_INDEX}")

    confirm = input("\n确认更新别名? (yes/no): ")
    if confirm.lower() != "yes":
        print("取消操作")
        return

    # 更新别名
    alias_body = {
        "actions": [
            {"remove": {"index": OLD_INDEX, "alias": ALIAS}},
            {"add": {"index": NEW_INDEX, "alias": ALIAS}},
        ]
    }

    response = requests.post(
        f"{HOST}/_aliases", auth=AUTH, json=alias_body, verify=False
    )

    if response.status_code == 200:
        print("✅ 别名更新成功!")

        # 验证别名指向
        alias_resp = requests.get(f"{HOST}/_alias/{ALIAS}", auth=AUTH, verify=False)
        if alias_resp.status_code == 200:
            current_indices = list(alias_resp.json().keys())
            print(f"✓ 别名 {ALIAS} 现在指向: {current_indices}")

        print(f"\n🎉 操作完成!")
        print(f"正式环境的 {ALIAS} 索引的 insert_time 字段已成功转换为 date 类型")
        print(f"现在可以正常进行数据同步了")

        # 询问是否删除旧索引
        print(f"\n旧索引 {OLD_INDEX} 仍然存在")
        delete_confirm = input("是否删除旧索引? 请输入 'DELETE' 确认: ")
        if delete_confirm == "DELETE":
            delete_resp = requests.delete(
                f"{HOST}/{OLD_INDEX}", auth=AUTH, verify=False
            )
            if delete_resp.status_code == 200:
                print(f"✅ 旧索引 {OLD_INDEX} 已删除")
            else:
                print(f"❌ 删除旧索引失败: {delete_resp.text}")
        else:
            print(f"保留旧索引 {OLD_INDEX}")

    else:
        print(f"❌ 别名更新失败: {response.text}")


if __name__ == "__main__":
    main()
