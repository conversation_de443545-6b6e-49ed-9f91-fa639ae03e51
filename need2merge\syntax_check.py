#!/usr/bin/env python3
"""
语法检查脚本
"""

import ast
import sys


def check_syntax(filename):
    """检查文件语法"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            content = f.read()

        # 尝试解析语法
        ast.parse(content)
        print(f"✓ {filename}: 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"✗ {filename}: 语法错误 - {e}")
        return False
    except Exception as e:
        print(f"✗ {filename}: 检查失败 - {e}")
        return False


def main():
    """主函数"""
    files_to_check = [
        "analyse_appendix.py",
        "analyse_appendix_asc.py",
        "analyse_noappendix.py",
    ]

    print("检查文件语法...")
    print("=" * 50)

    all_passed = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_passed = False

    print("=" * 50)
    if all_passed:
        print("🎉 所有文件语法检查通过！")
        return 0
    else:
        print("❌ 部分文件存在语法错误")
        return 1


if __name__ == "__main__":
    sys.exit(main())
