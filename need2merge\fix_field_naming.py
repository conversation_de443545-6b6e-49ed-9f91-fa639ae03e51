#!/usr/bin/env python3
"""
修复字段命名问题 - 将驼峰命名转换为下划线命名
"""

from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk
import re

# 标准字段（下划线命名）
STANDARD_FIELDS = {
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    "insert_time",
}

# 驼峰命名到下划线命名的映射
CAMEL_TO_SNAKE_MAPPING = {
    # 从验证结果中发现的驼峰字段
    "announcementType": "announcement_type",
    "bidCancelledFlag": "bid_cancelled_flag",
    "bidCancelledReason": "bid_cancelled_reason",
    "bidSubmissionDeadline": "bid_submission_deadline",
    "bidderContactPerson": "bidder_contact_person",
    "bidderContactPhoneNumber": "bidder_contact_phone_number",
    "bidderContractConfigParam": "bidder_contract_config_param",
    "bidderName": "bidder_name",
    "bidderPrice": "bidder_price",
    "fiscalDelegationNumber": "fiscal_delegation_number",
    "objectAmount": "object_amount",
    "objectBrand": "object_brand",
    "objectConf": "object_conf",
    "objectMaintenancePeriod": "object_maintenance_period",
    "objectModel": "object_model",
    "objectName": "object_name",
    "objectOEM": "object_oem",
    "objectPrice": "object_price",
    "objectPriceSource": "object_price_source",
    "objectProduceArea": "object_produce_area",
    "objectQuality": "object_quality",
    "objectSupplier": "object_supplier",
    "objectTotalPrice": "object_total_price",
    "objectUnit": "object_unit",
    "prjAddr": "prj_addr",
    "prjApprovalAuthority": "prj_approval_authority",
    "prjName": "prj_name",
    "prjNumber": "prj_number",
    "prjSubType": "prj_sub_type",
    "prjType": "prj_type",
    "procurementMethod": "procurement_method",
    "releaseTime": "release_time",
    "serviceFee": "service_fee",
    "superintendentOffice": "superintendent_office",
    "superintendentOfficeCode": "superintendent_office_code",
    "tradePlatform": "trade_platform",
}


def setup_es():
    """设置ES连接"""
    try:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=60,
        )
    except TypeError:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )
    return es


def analyze_field_naming():
    """分析字段命名问题"""
    es = setup_es()
    index_name = "markersweb_attachment_analysis_alias"

    print("分析字段命名问题")
    print("=" * 50)

    # 获取样本文档
    sample_response = es.search(
        index=index_name, body={"query": {"match_all": {}}, "size": 50}
    )

    all_fields = set()
    camel_fields = set()
    snake_fields = set()

    for doc in sample_response["hits"]["hits"]:
        doc_fields = doc["_source"].keys()
        all_fields.update(doc_fields)

        for field in doc_fields:
            if re.search(r"[A-Z]", field):  # 包含大写字母，可能是驼峰命名
                camel_fields.add(field)
            elif "_" in field:  # 包含下划线
                snake_fields.add(field)

    print(f"总字段数: {len(all_fields)}")
    print(f"驼峰命名字段数: {len(camel_fields)}")
    print(f"下划线命名字段数: {len(snake_fields)}")

    print(f"\n驼峰命名字段:")
    for field in sorted(camel_fields):
        mapped_field = CAMEL_TO_SNAKE_MAPPING.get(field, "未映射")
        print(f"  {field} -> {mapped_field}")

    # 检查是否有字段同时存在两种命名
    conflicts = []
    for camel_field, snake_field in CAMEL_TO_SNAKE_MAPPING.items():
        if camel_field in all_fields and snake_field in all_fields:
            conflicts.append((camel_field, snake_field))

    if conflicts:
        print(f"\n发现命名冲突 ({len(conflicts)} 对):")
        for camel, snake in conflicts:
            print(f"  {camel} 和 {snake} 同时存在")

    return camel_fields, snake_fields, conflicts


def fix_field_naming():
    """修复字段命名"""
    es = setup_es()
    index_name = "markersweb_attachment_analysis_alias"

    print("\n开始修复字段命名")
    print("=" * 50)

    # 分析当前状态
    camel_fields, snake_fields, conflicts = analyze_field_naming()

    if not camel_fields:
        print("✓ 没有发现驼峰命名字段")
        return

    # 确认操作
    print(f"\n将要处理 {len(camel_fields)} 个驼峰命名字段")
    confirm = input("确认要进行字段命名转换吗? (yes/no): ")
    if confirm.lower() != "yes":
        print("操作已取消")
        return

    # 统计信息
    processed_docs = 0
    updated_docs = 0
    errors = 0

    try:
        # 查找包含驼峰字段的文档
        camel_field_names = list(CAMEL_TO_SNAKE_MAPPING.keys())

        # 构建查询，查找包含任何驼峰字段的文档
        should_queries = []
        for field in camel_field_names:
            should_queries.append({"exists": {"field": field}})

        query = {
            "query": {"bool": {"should": should_queries, "minimum_should_match": 1}}
        }

        print(f"扫描包含驼峰字段的文档...")

        actions = []
        batch_size = 100

        for doc in scan(es, query=query, index=index_name, size=batch_size):
            processed_docs += 1
            doc_id = doc["_id"]
            doc_source = doc["_source"]

            # 检查是否需要更新
            needs_update = False
            updates = {}
            removes = []

            for camel_field, snake_field in CAMEL_TO_SNAKE_MAPPING.items():
                if camel_field in doc_source:
                    needs_update = True
                    value = doc_source[camel_field]

                    # 如果下划线字段不存在，则添加
                    if snake_field not in doc_source:
                        updates[snake_field] = value

                    # 标记删除驼峰字段
                    removes.append(camel_field)

            if needs_update:
                # 构建更新脚本
                script_parts = []

                # 添加新字段
                for field, value in updates.items():
                    script_parts.append(f"ctx._source['{field}'] = params.{field}")

                # 删除旧字段
                for field in removes:
                    script_parts.append(f"ctx._source.remove('{field}')")

                script = "; ".join(script_parts)

                action = {
                    "_op_type": "update",
                    "_index": index_name,
                    "_id": doc_id,
                    "script": {"source": script, "params": updates},
                }

                actions.append(action)
                updated_docs += 1

                if len(actions) >= batch_size:
                    # 执行批量更新
                    try:
                        success_count, failed_items = bulk(
                            es, actions, request_timeout=60
                        )
                        if failed_items:
                            errors += len(failed_items)
                            print(f"批量更新中有 {len(failed_items)} 个失败")
                    except Exception as e:
                        print(f"批量更新失败: {e}")
                        errors += len(actions)

                    actions = []

                    # 显示进度
                    print(
                        f"进度: 已处理 {processed_docs} 文档, 已更新 {updated_docs} 文档"
                    )

        # 处理剩余的操作
        if actions:
            try:
                success_count, failed_items = bulk(es, actions, request_timeout=60)
                if failed_items:
                    errors += len(failed_items)
                    print(f"最后批次更新中有 {len(failed_items)} 个失败")
            except Exception as e:
                print(f"最后批次更新失败: {e}")
                errors += len(actions)

        # 刷新索引
        es.indices.refresh(index=index_name)

        print(f"\n字段命名修复完成!")
        print(f"总处理文档数: {processed_docs}")
        print(f"需要更新的文档数: {updated_docs}")
        print(f"失败的文档数: {errors}")

    except Exception as e:
        print(f"修复过程中发生错误: {e}")


if __name__ == "__main__":
    fix_field_naming()
