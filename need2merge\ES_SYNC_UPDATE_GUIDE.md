# ES 增量同步脚本更新指南

## 更新内容

### 1. 同步脚本配置更新

**修改文件**: `sync_es_data_incremental.py`

**主要变更**:

- 支持不同索引使用不同的时间戳字段
- `chn_ylcg` 索引现在使用 `create_time` 字段进行筛选
- `markersweb_attachment_analysis_alias` 索引继续使用 `insert_time` 字段

**配置变更**:

```python
# 旧配置
"INDICES_TO_SYNC": ["chn_ylcg", "markersweb_attachment_analysis_alias"],
"TIMESTAMP_FIELD": "insert_time",

# 新配置
"INDICES_TO_SYNC": {
    "chn_ylcg": "create_time",
    "markersweb_attachment_analysis_alias": "insert_time"
},
```

### 2. ES 字段映射更新

**新增文件**: `update_es_mapping.py`

**功能**: 将 `markersweb_attachment_analysis_alias` 索引中的 `insert_time` 字段从 `text` 类型转换为 `date` 类型

**使用方法**:

```bash
python update_es_mapping.py
```

**注意事项**:

- 该脚本会创建新索引并重建数据
- 会自动更新别名指向
- 执行前请确保有足够的磁盘空间
- 建议在低峰期执行

### 3. 分析脚本更新

**修改文件**:

- `analyse_noappendix.py`
- `analyse_appendix.py`
- `analyse_appendix_asc.py`

**主要变更**:

- `insert_time` 字段现在插入 `datetime` 对象而不是字符串
- 确保插入 ES 时为 `date` 类型

**代码变更**:

```python
# 旧代码
"insert_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),

# 新代码
"insert_time": datetime.now(),
```

## 使用指南

### 1. 测试同步脚本

```bash
# 测试 chn_ylcg 索引同步（使用 create_time）
python sync_es_data_incremental.py test "2025-06-19 00:00:00" "2025-06-19 23:59:59"

# 测试 markersweb_attachment_analysis_alias 索引同步（使用 insert_time）
python sync_es_data_incremental.py test "2025-06-18 11:30:00" "2025-06-18 11:40:00"
```

### 2. 更新 ES 字段映射（已完成）

✅ `insert_time` 字段已成功转换为 `date` 类型：

```bash
python update_es_mapping.py
```

**执行结果**:

- 原索引: `markersweb_attachment_analysis_v3`
- 新索引: `markersweb_attachment_analysis_v3_date_20250728_093204`
- 字段类型: `text` → `date`
- 格式支持: `yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis`

### 3. 正常运行同步脚本

```bash
# 启动定时同步（每日 09:00 执行）
python sync_es_data_incremental.py
```

## 验证结果

### ✅ 1. 验证同步脚本

运行测试命令后，检查日志输出：

- ✅ `chn_ylcg` 索引正确使用 `create_time` 字段
- ✅ `markersweb_attachment_analysis_alias` 索引正确使用 `insert_time` 字段
- ✅ 能正常同步数据（测试同步了 13 个文档）

### ✅ 2. 验证字段类型

在 ES 中检查字段映射：

```bash
# 检查 insert_time 字段类型（PowerShell）
Invoke-RestMethod -Uri "http://172.18.10.8:9200/markersweb_attachment_analysis_alias/_mapping" -Method GET -Headers @{Authorization = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("elastic:elastic"))}
```

✅ 已确认字段类型正确：

```json
{
  "insert_time": {
    "type": "date",
    "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
  }
}
```

### 3. 验证数据插入

运行分析脚本后，检查新插入的数据：

```bash
# 查询最新数据
curl -X GET "http://172.18.10.8:9200/markersweb_attachment_analysis_alias/_search" \
  -u elastic:elastic \
  -H "Content-Type: application/json" \
  -d '{
    "query": {"match_all": {}},
    "sort": [{"insert_time": {"order": "desc"}}],
    "size": 1
  }'
```

## 注意事项

1. **备份数据**: 在执行字段映射更新前，建议备份重要数据
2. **磁盘空间**: 重建索引需要额外的磁盘空间
3. **执行时间**: 建议在业务低峰期执行映射更新
4. **测试验证**: 在生产环境执行前，建议在测试环境先验证
5. **监控日志**: 执行过程中密切关注日志输出

## 故障排除

### 1. 同步脚本问题

**问题**: 索引同步失败
**解决**: 检查时间戳字段是否存在，确认字段类型

**问题**: 时间范围查询无结果
**解决**: 检查时间格式，确认数据时间范围

### 2. 字段映射更新问题

**问题**: 重建索引失败
**解决**: 检查磁盘空间，确认 ES 集群状态

**问题**: 数据量不一致
**解决**: 检查重建过程中是否有数据写入，必要时重新执行

### 3. 分析脚本问题

**问题**: 插入数据时字段类型错误
**解决**: 确认 ES 映射已更新，检查 Python datetime 对象序列化

## 联系支持

如遇到问题，请提供：

1. 错误日志
2. ES 集群状态
3. 执行的具体命令
4. 数据样本（脱敏后）
