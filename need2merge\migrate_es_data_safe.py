#!/usr/bin/env python3
"""
安全的ES数据迁移脚本

将测试环境的ES索引和数据安全地复制到生产环境，包含备份和回滚功能
"""

import os
import sys
import json
import time
import shutil
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

import requests
from requests.auth import HTTPBasicAuth
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置信息
CONFIG = {
    # 生产环境ES集群配置
    "PROD_ES_HOST": [
        "http://**********:9200",
        "http://**********:9200",
        "http://**********:9200",
    ],
    "PROD_ES_USER": "elastic",
    "PROD_ES_PASS": "W8DOwJ2xs4mBV4BcNBNi",
    # 测试ES集群配置
    "TEST_ES_HOST": "http://***********:9200",
    "TEST_ES_USER": "elastic",
    "TEST_ES_PASS": "elastic",
    # 要迁移的索引
    "INDICES_TO_MIGRATE": ["markersweb_attachment_analysis_v3"],
    # 批处理大小
    "BATCH_SIZE": 500,
    # 超时设置
    "TIMEOUT": 60,
    # 重试次数
    "MAX_RETRIES": 3,
    # 备份目录
    "BACKUP_DIR": "./es_backup",
}


class SafeESMigrator:
    """安全的ES数据迁移器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.prod_hosts = config["PROD_ES_HOST"]
        self.prod_auth = HTTPBasicAuth(config["PROD_ES_USER"], config["PROD_ES_PASS"])
        self.test_host = config["TEST_ES_HOST"]
        self.test_auth = HTTPBasicAuth(config["TEST_ES_USER"], config["TEST_ES_PASS"])
        self.timeout = config["TIMEOUT"]
        self.max_retries = config["MAX_RETRIES"]

        # 备份目录
        self.backup_dir = Path(config["BACKUP_DIR"])
        self.backup_dir.mkdir(exist_ok=True)

        # 当前使用的生产环境主机
        self.prod_host = None

        # 统计信息
        self.stats = {
            "indices_migrated": 0,
            "documents_migrated": 0,
            "errors": 0,
            "backups_created": 0,
            "start_time": None,
            "end_time": None,
        }

    def _get_available_prod_host(self) -> Optional[str]:
        """获取可用的生产ES主机"""
        for host in self.prod_hosts:
            try:
                response = requests.get(
                    f"{host}/_cluster/health",
                    auth=self.prod_auth,
                    timeout=10,
                    verify=False,
                )
                if response.status_code == 200:
                    health = response.json()
                    if health.get("status") in ["green", "yellow"]:
                        print(
                            f"✓ 使用生产ES主机: {host} (状态: {health.get('status')})"
                        )
                        return host
            except Exception as e:
                print(f"✗ 生产ES主机 {host} 不可用: {e}")
                continue
        return None

    def _test_connections(self) -> bool:
        """测试ES连接"""
        print("=" * 60)
        print("测试ES连接")
        print("=" * 60)

        # 测试生产ES连接
        prod_host = self._get_available_prod_host()
        if not prod_host:
            print("❌ 所有生产ES主机都不可用")
            return False

        self.prod_host = prod_host

        # 测试测试环境ES连接
        try:
            response = requests.get(
                f"{self.test_host}/_cluster/health",
                auth=self.test_auth,
                timeout=self.timeout,
                verify=False,
            )
            if response.status_code == 200:
                health = response.json()
                print(
                    f"✓ 测试环境ES连接正常: {self.test_host} (状态: {health.get('status')})"
                )
            else:
                print(f"❌ 测试环境ES连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 测试环境ES连接异常: {e}")
            return False

        return True

    def _backup_index(self, index_name: str) -> bool:
        """备份生产环境的索引"""
        print(f"\n--- 备份生产环境索引: {index_name} ---")

        backup_file = self.backup_dir / f"{index_name}_backup_{int(time.time())}.json"

        try:
            # 检查索引是否存在
            response = requests.head(
                f"{self.prod_host}/{index_name}",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code == 404:
                print(f"⚠️ 生产环境索引 {index_name} 不存在，无需备份")
                return True
            elif response.status_code != 200:
                print(f"❌ 检查索引状态失败: {response.status_code}")
                return False

            # 获取索引设置和映射
            settings_response = requests.get(
                f"{self.prod_host}/{index_name}/_settings",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            mapping_response = requests.get(
                f"{self.prod_host}/{index_name}/_mapping",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            if (
                settings_response.status_code != 200
                or mapping_response.status_code != 200
            ):
                print(f"❌ 获取索引配置失败")
                return False

            # 获取文档数据
            scroll_response = requests.post(
                f"{self.prod_host}/{index_name}/_search?scroll=5m",
                auth=self.prod_auth,
                json={"size": 1000, "query": {"match_all": {}}, "_source": True},
                timeout=self.timeout,
                verify=False,
            )

            if scroll_response.status_code != 200:
                print(f"❌ 获取文档数据失败: {scroll_response.status_code}")
                return False

            # 保存备份数据
            backup_data = {
                "index_name": index_name,
                "backup_time": datetime.now().isoformat(),
                "settings": settings_response.json(),
                "mappings": mapping_response.json(),
                "documents": [],
            }

            scroll_data = scroll_response.json()
            scroll_id = scroll_data.get("_scroll_id")
            hits = scroll_data.get("hits", {}).get("hits", [])

            doc_count = 0
            while hits:
                for hit in hits:
                    backup_data["documents"].append(
                        {"_id": hit["_id"], "_source": hit["_source"]}
                    )
                    doc_count += 1

                # 继续滚动
                if scroll_id:
                    scroll_response = requests.post(
                        f"{self.prod_host}/_search/scroll",
                        auth=self.prod_auth,
                        json={"scroll": "5m", "scroll_id": scroll_id},
                        timeout=self.timeout,
                        verify=False,
                    )

                    if scroll_response.status_code == 200:
                        scroll_data = scroll_response.json()
                        scroll_id = scroll_data.get("_scroll_id")
                        hits = scroll_data.get("hits", {}).get("hits", [])
                    else:
                        break
                else:
                    break

            # 清理滚动上下文
            if scroll_id:
                requests.delete(
                    f"{self.prod_host}/_search/scroll",
                    auth=self.prod_auth,
                    json={"scroll_id": [scroll_id]},
                    timeout=self.timeout,
                    verify=False,
                )

            # 保存备份文件
            with open(backup_file, "w", encoding="utf-8") as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            print(f"✓ 备份完成: {backup_file} ({doc_count} 个文档)")
            self.stats["backups_created"] += 1
            return True

        except Exception as e:
            print(f"❌ 备份异常: {e}")
            return False

    def _delete_index_if_exists(self, index_name: str) -> bool:
        """删除索引（如果存在）"""
        try:
            response = requests.delete(
                f"{self.prod_host}/{index_name}",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code in [200, 404]:
                return True
            else:
                print(f"❌ 删除索引失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 删除索引异常: {e}")
            return False

    def _create_index_in_production(self, index_name: str) -> bool:
        """在生产环境创建索引"""
        print(f"\n--- 在生产环境创建索引: {index_name} ---")

        try:
            # 获取测试环境的索引映射和设置
            mapping_response = requests.get(
                f"{self.test_host}/{index_name}/_mapping",
                auth=self.test_auth,
                timeout=self.timeout,
                verify=False,
            )

            settings_response = requests.get(
                f"{self.test_host}/{index_name}/_settings",
                auth=self.test_auth,
                timeout=self.timeout,
                verify=False,
            )

            if (
                mapping_response.status_code != 200
                or settings_response.status_code != 200
            ):
                print(f"❌ 无法获取测试环境索引 {index_name} 的配置")
                return False

            mapping_data = mapping_response.json()
            settings_data = settings_response.json()

            # 提取映射和设置
            index_mapping = mapping_data[index_name]["mappings"]
            index_settings = settings_data[index_name]["settings"]["index"]

            # 清理设置中的系统字段
            cleaned_settings = {}
            for key, value in index_settings.items():
                if not key.startswith(
                    ("uuid", "version", "provided_name", "creation_date", "routing")
                ):
                    cleaned_settings[key] = value

            # 构建创建索引的请求体
            create_body = {
                "settings": {"index": cleaned_settings},
                "mappings": index_mapping,
            }

            # 在生产环境创建索引
            response = requests.put(
                f"{self.prod_host}/{index_name}",
                auth=self.prod_auth,
                json=create_body,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code in [200, 201]:
                print(f"✓ 生产环境索引 {index_name} 创建成功")
                return True
            else:
                print(f"❌ 创建索引失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 创建索引异常: {e}")
            return False

    def _migrate_documents(self, index_name: str) -> bool:
        """迁移文档数据"""
        print(f"\n--- 迁移文档数据: {index_name} ---")

        try:
            # 获取测试环境文档数量
            count_response = requests.get(
                f"{self.test_host}/{index_name}/_count",
                auth=self.test_auth,
                timeout=self.timeout,
                verify=False,
            )

            if count_response.status_code != 200:
                print(f"❌ 获取文档数量失败: {count_response.status_code}")
                return False

            total_docs = count_response.json()["count"]
            print(f"测试环境文档数量: {total_docs}")

            if total_docs == 0:
                print("⚠️ 测试环境没有文档数据")
                return True

            migrated_count = 0

            # 初始化滚动搜索
            scroll_response = requests.post(
                f"{self.test_host}/{index_name}/_search?scroll=5m",
                auth=self.test_auth,
                json={
                    "size": self.config["BATCH_SIZE"],
                    "query": {"match_all": {}},
                    "_source": True,
                },
                timeout=self.timeout,
                verify=False,
            )

            if scroll_response.status_code != 200:
                print(f"❌ 初始化滚动搜索失败: {scroll_response.status_code}")
                return False

            scroll_data = scroll_response.json()
            scroll_id = scroll_data.get("_scroll_id")
            hits = scroll_data.get("hits", {}).get("hits", [])

            while hits:
                # 准备批量插入数据
                bulk_data = []

                for hit in hits:
                    # 构建索引操作
                    index_action = {"index": {"_index": index_name, "_id": hit["_id"]}}

                    bulk_data.append(json.dumps(index_action))
                    bulk_data.append(json.dumps(hit["_source"]))

                # 执行批量插入
                bulk_body = "\n".join(bulk_data) + "\n"

                for retry in range(self.max_retries):
                    try:
                        bulk_response = requests.post(
                            f"{self.prod_host}/_bulk",
                            auth=self.prod_auth,
                            data=bulk_body,
                            headers={"Content-Type": "application/x-ndjson"},
                            timeout=self.timeout * 2,
                            verify=False,
                        )

                        if bulk_response.status_code == 200:
                            bulk_result = bulk_response.json()
                            errors = bulk_result.get("errors", False)

                            if not errors:
                                migrated_count += len(hits)
                                print(
                                    f"✓ 已迁移 {migrated_count}/{total_docs} 个文档 ({migrated_count/total_docs*100:.1f}%)"
                                )
                                break
                            else:
                                print(f"⚠️ 批量插入有错误")
                                for item in bulk_result.get("items", []):
                                    if "index" in item and "error" in item["index"]:
                                        print(f"   错误: {item['index']['error']}")
                                self.stats["errors"] += 1
                                break
                        else:
                            if retry < self.max_retries - 1:
                                print(
                                    f"⚠️ 批量插入失败，重试 {retry + 1}/{self.max_retries}: {bulk_response.status_code}"
                                )
                                time.sleep(2**retry)
                            else:
                                print(
                                    f"❌ 批量插入最终失败: {bulk_response.status_code}"
                                )
                                self.stats["errors"] += 1
                                return False

                    except Exception as e:
                        if retry < self.max_retries - 1:
                            print(
                                f"⚠️ 批量插入异常，重试 {retry + 1}/{self.max_retries}: {e}"
                            )
                            time.sleep(2**retry)
                        else:
                            print(f"❌ 批量插入最终异常: {e}")
                            self.stats["errors"] += 1
                            return False

                # 继续滚动搜索
                if scroll_id:
                    scroll_response = requests.post(
                        f"{self.test_host}/_search/scroll",
                        auth=self.test_auth,
                        json={"scroll": "5m", "scroll_id": scroll_id},
                        timeout=self.timeout,
                        verify=False,
                    )

                    if scroll_response.status_code == 200:
                        scroll_data = scroll_response.json()
                        scroll_id = scroll_data.get("_scroll_id")
                        hits = scroll_data.get("hits", {}).get("hits", [])
                    else:
                        print(f"❌ 滚动搜索失败: {scroll_response.status_code}")
                        break
                else:
                    break

            # 清理滚动上下文
            if scroll_id:
                requests.delete(
                    f"{self.test_host}/_search/scroll",
                    auth=self.test_auth,
                    json={"scroll_id": [scroll_id]},
                    timeout=self.timeout,
                    verify=False,
                )

            print(f"✓ 索引 {index_name} 迁移完成: {migrated_count} 个文档")
            self.stats["documents_migrated"] += migrated_count

            # 刷新索引
            requests.post(
                f"{self.prod_host}/{index_name}/_refresh",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            return True

        except Exception as e:
            print(f"❌ 迁移文档异常: {e}")
            self.stats["errors"] += 1
            return False

    def _verify_migration(self, index_name: str) -> bool:
        """验证迁移结果"""
        print(f"\n--- 验证迁移结果: {index_name} ---")

        try:
            # 等待索引刷新
            time.sleep(2)

            # 获取测试环境和生产环境的文档数量
            test_response = requests.get(
                f"{self.test_host}/{index_name}/_count",
                auth=self.test_auth,
                timeout=self.timeout,
                verify=False,
            )

            prod_response = requests.get(
                f"{self.prod_host}/{index_name}/_count",
                auth=self.prod_auth,
                timeout=self.timeout,
                verify=False,
            )

            if test_response.status_code != 200 or prod_response.status_code != 200:
                print(f"❌ 获取文档数量失败")
                return False

            test_count = test_response.json()["count"]
            prod_count = prod_response.json()["count"]

            print(f"测试环境文档数量: {test_count}")
            print(f"生产环境文档数量: {prod_count}")

            if prod_count >= test_count:
                print(f"✓ 索引 {index_name} 验证通过")
                return True
            else:
                print(f"❌ 索引 {index_name} 验证失败: 文档数量不匹配")
                return False

        except Exception as e:
            print(f"❌ 验证异常: {e}")
            return False

    def migrate_index(self, index_name: str) -> bool:
        """安全迁移单个索引"""
        print(f"\n{'='*60}")
        print(f"开始迁移索引: {index_name}")
        print(f"{'='*60}")

        # 1. 备份生产环境索引
        if not self._backup_index(index_name):
            print(f"❌ 备份失败，跳过索引 {index_name}")
            return False

        # 2. 删除生产环境索引（如果存在）
        if not self._delete_index_if_exists(index_name):
            print(f"❌ 删除索引失败，跳过索引 {index_name}")
            return False

        # 3. 在生产环境创建索引
        if not self._create_index_in_production(index_name):
            print(f"❌ 创建索引失败，跳过索引 {index_name}")
            return False

        # 4. 迁移文档数据
        if not self._migrate_documents(index_name):
            print(f"❌ 迁移文档失败，跳过索引 {index_name}")
            return False

        # 5. 验证迁移结果
        if not self._verify_migration(index_name):
            print(f"❌ 验证失败，跳过索引 {index_name}")
            return False

        self.stats["indices_migrated"] += 1
        print(f"✓ 索引 {index_name} 迁移成功")
        return True

    def run_migration(self) -> bool:
        """执行完整迁移"""
        print("=" * 60)
        print("安全ES数据迁移开始")
        print("=" * 60)
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试环境: {self.config['TEST_ES_HOST']}")
        print(f"生产环境: {self.config['PROD_ES_HOST']}")
        print(f"迁移索引: {', '.join(self.config['INDICES_TO_MIGRATE'])}")
        print(f"备份目录: {self.backup_dir}")

        self.stats["start_time"] = time.time()

        # 测试连接
        if not self._test_connections():
            return False

        # 迁移每个索引
        success_count = 0
        total_count = len(self.config["INDICES_TO_MIGRATE"])

        for index_name in self.config["INDICES_TO_MIGRATE"]:
            try:
                if self.migrate_index(index_name):
                    success_count += 1
                else:
                    print(f"❌ 索引 {index_name} 迁移失败")
            except Exception as e:
                print(f"❌ 索引 {index_name} 迁移异常: {e}")
                self.stats["errors"] += 1

        self.stats["end_time"] = time.time()

        # 输出迁移结果
        self._print_migration_summary(success_count, total_count)

        return success_count == total_count

    def _print_migration_summary(self, success_count: int, total_count: int):
        """打印迁移摘要"""
        duration = self.stats["end_time"] - self.stats["start_time"]

        print("\n" + "=" * 60)
        print("迁移结果摘要")
        print("=" * 60)
        print(f"总索引数: {total_count}")
        print(f"成功迁移: {success_count}")
        print(f"失败数量: {total_count - success_count}")
        print(f"文档迁移: {self.stats['documents_migrated']}")
        print(f"备份创建: {self.stats['backups_created']}")
        print(f"错误数量: {self.stats['errors']}")
        print(f"耗时: {duration:.2f} 秒")
        print(f"备份位置: {self.backup_dir}")

        if success_count == total_count:
            print("🎉 所有索引迁移成功！")
        else:
            print("❌ 部分索引迁移失败")
            print("💡 可以使用备份文件进行回滚")


def main():
    """主函数"""
    print("安全ES数据迁移工具")
    print("将测试环境数据安全地复制到生产环境")

    # 显示配置信息
    print(f"\n📋 配置信息:")
    print(f"   测试环境: {CONFIG['TEST_ES_HOST']}")
    print(f"   生产环境: {CONFIG['PROD_ES_HOST']}")
    print(f"   迁移索引: {', '.join(CONFIG['INDICES_TO_MIGRATE'])}")
    print(f"   备份目录: {CONFIG['BACKUP_DIR']}")
    print(f"   批处理大小: {CONFIG['BATCH_SIZE']}")

    # 安全确认
    print(f"\n⚠️  重要提醒:")
    print(f"   1. 此操作将覆盖生产环境的现有数据")
    print(f"   2. 操作前会自动创建备份")
    print(f"   3. 请确保生产环境ES集群状态正常")
    print(f"   4. 建议在维护窗口期间执行")

    confirm1 = input("\n确认配置信息正确? (输入 'yes' 确认): ")
    if confirm1.lower() != "yes":
        print("❌ 操作已取消")
        return 1

    confirm2 = input("确认执行迁移操作? (输入 'YES' 最终确认): ")
    if confirm2 != "YES":
        print("❌ 操作已取消")
        return 1

    # 执行迁移
    migrator = SafeESMigrator(CONFIG)

    try:
        success = migrator.run_migration()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 迁移过程异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
