#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引名称
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_v3")
print(f"使用索引: {index_name}")

# 定义正确的查询 - 使用 match_phrase_prefix 而不是 prefix
query = {
    "query": {
        "bool": {
            "must": [
                {"match_phrase_prefix": {"source_url": "https://www.xinecai.com"}},
                {"term": {"source_category": "001"}},
            ]
        }
    },
    "size": 0,  # 只需要计数，不需要返回文档内容
}

try:
    # 执行查询
    response = es.search(index=index_name, body=query)

    # 获取匹配的文档数量
    count = response["hits"]["total"]["value"]
    print(f"符合条件的文档数量: {count}")
    print(
        f"查询条件: source_url 以 'https://www.xinecai.com' 开头且 source_category 等于 '001'"
    )

    # 获取一些示例文档
    if count > 0:
        sample_query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase_prefix": {
                                "source_url": "https://www.xinecai.com"
                            }
                        },
                        {"term": {"source_category": "001"}},
                    ]
                }
            },
            "size": 3,  # 获取3个示例文档
        }

        sample_response = es.search(index=index_name, body=sample_query)
        print(f"\n示例文档 (共 {len(sample_response['hits']['hits'])} 个):")

        for i, hit in enumerate(sample_response["hits"]["hits"]):
            doc = hit["_source"]
            print(f"\n示例 {i+1}:")
            print(f"source_url: {doc.get('source_url', 'N/A')}")
            print(f"source_category: {doc.get('source_category', 'N/A')}")

            # 打印其他一些可能有用的字段
            interesting_fields = [
                "release_time",
                "province",
                "city",
                "county",
                "object_name",
            ]
            for field in interesting_fields:
                if field in doc:
                    print(f"{field}: {doc[field]}")

except Exception as e:
    print(f"查询执行失败: {str(e)}")
    exit(1)
