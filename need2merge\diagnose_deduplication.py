#!/usr/bin/env python3
"""
诊断去重脚本执行问题
"""

import sys
from dotenv import load_dotenv

from es_deal import init_es_client
from utils.log_cfg import log


def check_index_mapping(es_client, index_name: str):
    """检查索引mapping"""
    try:
        log.info("检查索引mapping...")
        
        mapping = es_client.indices.get_mapping(index=index_name)
        properties = mapping[index_name]['mappings'].get('properties', {})
        
        if 'url' in properties:
            url_mapping = properties['url']
            log.info(f"url字段mapping: {url_mapping}")
            
            # 检查是否有keyword子字段
            if url_mapping.get('type') == 'text':
                fields = url_mapping.get('fields', {})
                if 'keyword' in fields:
                    log.info("✓ url字段有keyword子字段，可以进行精确聚合")
                else:
                    log.warning("✗ url字段没有keyword子字段，聚合可能失败")
            elif url_mapping.get('type') == 'keyword':
                log.info("✓ url字段是keyword类型，可以进行精确聚合")
            else:
                log.warning(f"✗ url字段类型未知: {url_mapping.get('type')}")
        else:
            log.error("✗ 索引中没有url字段")
            
    except Exception as e:
        log.error(f"检查mapping失败: {e}")


def test_url_aggregation(es_client, index_name: str):
    """测试url聚合查询"""
    try:
        log.info("测试url聚合查询...")
        
        # 先尝试使用url.keyword
        try:
            agg_query = {
                "size": 0,
                "aggs": {
                    "url_count": {
                        "terms": {
                            "field": "url.keyword",
                            "size": 10
                        }
                    }
                }
            }
            
            result = es_client.search(index=index_name, body=agg_query)
            buckets = result.get("aggregations", {}).get("url_count", {}).get("buckets", [])
            
            log.info(f"✓ url.keyword聚合成功，返回 {len(buckets)} 个url")
            
            # 检查是否有重复
            duplicates = [bucket for bucket in buckets if bucket['doc_count'] > 1]
            log.info(f"发现 {len(duplicates)} 个重复的url")
            
            if duplicates:
                log.info("重复url示例:")
                for i, bucket in enumerate(duplicates[:3]):
                    log.info(f"  {i+1}. {bucket['key']} (文档数: {bucket['doc_count']})")
            
        except Exception as e:
            log.warning(f"url.keyword聚合失败: {e}")
            
            # 尝试使用url字段直接聚合
            try:
                agg_query = {
                    "size": 0,
                    "aggs": {
                        "url_count": {
                            "terms": {
                                "field": "url",
                                "size": 10
                            }
                        }
                    }
                }
                
                result = es_client.search(index=index_name, body=agg_query)
                buckets = result.get("aggregations", {}).get("url_count", {}).get("buckets", [])
                
                log.info(f"✓ url字段直接聚合成功，返回 {len(buckets)} 个url")
                
            except Exception as e2:
                log.error(f"url字段直接聚合也失败: {e2}")
                
    except Exception as e:
        log.error(f"测试聚合查询失败: {e}")


def check_sample_documents(es_client, index_name: str):
    """检查样本文档"""
    try:
        log.info("检查样本文档...")
        
        query = {
            "size": 5,
            "_source": ["url", "appendix"]
        }
        
        result = es_client.search(index=index_name, body=query)
        hits = result.get("hits", {}).get("hits", [])
        
        log.info(f"获取到 {len(hits)} 个样本文档:")
        
        for i, hit in enumerate(hits):
            source = hit["_source"]
            url = source.get("url", "无url字段")
            appendix = source.get("appendix")
            appendix_info = f"类型: {type(appendix)}, 长度: {len(appendix) if isinstance(appendix, list) else 'N/A'}"
            
            log.info(f"  文档{i+1}: ID={hit['_id']}")
            log.info(f"    url: {url}")
            log.info(f"    appendix: {appendix_info}")
            
    except Exception as e:
        log.error(f"检查样本文档失败: {e}")


def main():
    """主函数"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()
        
        index_name = "chn_ylcg"
        
        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            sys.exit(1)
        
        log.info("=" * 60)
        log.info("诊断去重脚本执行问题")
        log.info("=" * 60)
        
        # 获取基本信息
        stats = es.indices.stats(index=index_name)
        doc_count = stats['indices'][index_name]['total']['docs']['count']
        log.info(f"索引文档总数: {doc_count:,}")
        
        # 检查mapping
        check_index_mapping(es, index_name)
        log.info("")
        
        # 测试聚合
        test_url_aggregation(es, index_name)
        log.info("")
        
        # 检查样本文档
        check_sample_documents(es, index_name)
        
        log.info("=" * 60)
        log.info("诊断完成")
        log.info("=" * 60)
        
    except KeyboardInterrupt:
        log.info("用户中断诊断")
        sys.exit(0)
    except Exception as e:
        log.error(f"诊断失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
