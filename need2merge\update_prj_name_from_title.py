#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
import re
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引名称
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_alias")
print(f"使用索引: {index_name}")

# 定义查询 - 查找prj_name为空且source_url以https://www.xinecai.com开头的文档
query = {
    "query": {
        "bool": {
            "must": [
                {"match_phrase_prefix": {"source_url": "https://www.xinecai.com"}},
                {
                    "bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "prj_name"}}}},
                            {"term": {"prj_name": ""}},
                        ],
                        "minimum_should_match": 1,
                    }
                },
                {"exists": {"field": "source_title"}},  # 确保source_title字段存在
            ]
        }
    },
    "size": 100,  # 每批处理100个文档
    "_source": ["source_url", "source_title", "prj_name"],
}

# 定义正则表达式模式
# 匹配完整的"中国科大附一院（安徽省立医院）XXX项目"或"中国科学技术大学附属第一医院（安徽省立医院）YYY项目"
# 使用更宽松的模式匹配任何字符，直到"项目"出现
pattern1 = r"(中国科大附一院（安徽省立医院）.*?项目)"
pattern2 = r"(中国科学技术大学附属第一医院（安徽省立医院）.*?项目)"
pattern3 = r"(中国科大附一院（安徽省立医院）.*?)招标公告"
pattern4 = r"(中国科学技术大学附属第一医院（安徽省立医院）.*?)入围结果公告"
pattern5 = r"(中国科大附一院（安徽省立医院）.*?)比选采购信息"

# 统计变量
total_docs = 0
updated_docs = 0
failed_docs = 0
no_match_docs = 0

try:
    # 使用scroll API来处理大量文档
    page = es.search(index=index_name, body=query, scroll="2m")  # 保持搜索上下文2分钟

    scroll_id = page["_scroll_id"]
    hits = page["hits"]["hits"]
    total_hits = page["hits"]["total"]["value"]

    print(f"找到符合条件的文档总数: {total_hits}")

    # 处理所有批次的文档
    while len(hits) > 0:
        for hit in hits:
            total_docs += 1
            doc_id = hit["_id"]
            source = hit["_source"]
            source_title = source.get("source_title", "").strip().replace(" ", "")

            # 应用正则表达式
            match = (
                re.search(pattern1, source_title)
                or re.search(pattern2, source_title)
                or re.search(pattern3, source_title)
                or re.search(pattern4, source_title)
                or re.search(pattern5, source_title)
            )

            if match:
                project_name = match.group(1)
                print(f"\n文档 {doc_id}:")
                print(f"原始标题: {source_title}")
                print(f"提取的项目名: {project_name}")

                try:
                    # 更新文档
                    update_response = es.update(
                        index=index_name,
                        id=doc_id,
                        body={"doc": {"prj_name": project_name}},
                    )

                    if update_response["result"] == "updated":
                        print(f"✅ 成功更新")
                        updated_docs += 1
                    else:
                        print(f"❌ 更新失败: {update_response}")
                        failed_docs += 1

                except Exception as e:
                    print(f"❌ 更新文档时出错: {str(e)}")
                    failed_docs += 1
            else:
                no_match_docs += 1
                if total_docs <= 10:  # 只显示前10个无匹配的文档，避免输出过多
                    print(f"\n文档 {doc_id} 标题不匹配模式: {source_title[:100]}...")

        # 获取下一批结果
        page = es.scroll(scroll_id=scroll_id, scroll="2m")
        scroll_id = page["_scroll_id"]
        hits = page["hits"]["hits"]

        # 显示进度
        print(f"\n已处理: {total_docs}/{total_hits} 文档")

    # 清理scroll上下文
    es.clear_scroll(scroll_id=scroll_id)

    # 打印最终统计信息
    print("\n=== 处理完成 ===")
    print(f"总共处理文档数: {total_docs}")
    print(f"成功更新文档数: {updated_docs}")
    print(f"更新失败文档数: {failed_docs}")
    print(f"标题不匹配文档数: {no_match_docs}")

except Exception as e:
    print(f"处理过程中出错: {str(e)}")
    exit(1)
