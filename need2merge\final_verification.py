#!/usr/bin/env python3
"""
最终验证清理结果
"""

from elasticsearch import Elasticsearch

STANDARD_FIELDS = {
    # 业务数据字段（40个）
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    # 源数据元数据字段（6个）
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    # 附件相关字段（8个）
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    # 系统字段（1个）
    "insert_time",
}


def final_verification():
    """最终验证"""
    # 连接测试环境
    try:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"],
            basic_auth=("elastic", "elastic"),
            request_timeout=30,
        )
    except TypeError:
        es = Elasticsearch(
            ["http://172.18.10.8:9200"], http_auth=("elastic", "elastic")
        )

    index_name = "markersweb_attachment_analysis_alias"

    print("最终验证清理结果")
    print("=" * 50)

    try:
        # 强制刷新索引
        es.indices.refresh(index=index_name)
        print("✓ 索引已刷新")

        # 1. 检查文档中的实际字段
        print(f"\n1. 检查文档中的实际字段:")
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10000}
        )

        all_doc_fields = set()
        nonstandard_count = {}

        for doc in sample_response["hits"]["hits"]:
            doc_fields = set(doc["_source"].keys())
            all_doc_fields.update(doc_fields)

            # 统计非标准字段
            for field in doc_fields:
                if field not in STANDARD_FIELDS:
                    nonstandard_count[field] = nonstandard_count.get(field, 0) + 1

        doc_nonstandard = all_doc_fields - STANDARD_FIELDS

        print(f"   样本文档数: {len(sample_response['hits']['hits'])}")
        print(f"   文档中总字段数: {len(all_doc_fields)}")
        print(f"   文档中非标准字段数: {len(doc_nonstandard)}")

        if doc_nonstandard:
            print("   文档中的非标准字段:")
            for field in sorted(doc_nonstandard):
                count = nonstandard_count.get(field, 0)
                print(
                    f"     - {field}: {count}/{len(sample_response['hits']['hits'])} 文档"
                )
        else:
            print("   ✓ 文档中没有非标准字段")

        # 2. 检查特定的非标准字段
        print(f"\n2. 检查特定非标准字段:")

        # 检查 contract_link
        contract_link_query = {"query": {"exists": {"field": "contract_link"}}}

        contract_link_count = es.count(index=index_name, body=contract_link_query)[
            "count"
        ]
        print(f"   包含 contract_link 字段的文档数: {contract_link_count}")

        if contract_link_count == 0:
            print("   ✓ contract_link 字段已完全删除")
        else:
            print(f"   ✗ 仍有 {contract_link_count} 个文档包含 contract_link 字段")

            # 显示包含该字段的文档
            sample_docs = es.search(index=index_name, body=contract_link_query, size=3)

            print("   包含 contract_link 的样本文档:")
            for i, doc in enumerate(sample_docs["hits"]["hits"], 1):
                contract_link_value = doc["_source"].get("contract_link", "N/A")
                print(f"     文档 {i}: contract_link = {contract_link_value}")

        # 3. 映射分析
        print(f"\n3. 映射分析:")
        mapping = es.indices.get_mapping(index=index_name)

        mapping_fields = set()
        for index, mapping_data in mapping.items():
            properties = mapping_data.get("mappings", {}).get("properties", {})
            mapping_fields.update(properties.keys())

        mapping_nonstandard = mapping_fields - STANDARD_FIELDS

        print(f"   映射中总字段数: {len(mapping_fields)}")
        print(f"   映射中非标准字段数: {len(mapping_nonstandard)}")

        # 4. 总结
        print(f"\n4. 清理结果总结:")

        if not doc_nonstandard:
            print("   ✓ 文档内容清理完成 - 所有文档只包含标准字段")
        else:
            print(f"   ✗ 文档内容未完全清理 - 仍有 {len(doc_nonstandard)} 个非标准字段")

        if mapping_nonstandard:
            print(f"   ⚠️  映射中仍有 {len(mapping_nonstandard)} 个历史字段定义")
            print("      这些字段定义不影响功能，但如需完全清理需要重建索引")
        else:
            print("   ✓ 映射也已完全清理")

        # 5. 建议
        print(f"\n5. 建议:")
        if not doc_nonstandard and not mapping_nonstandard:
            print("   🎉 清理完成！索引已完全符合标准字段要求")
        elif not doc_nonstandard:
            print("   📝 文档内容已清理完成，映射中的历史字段可以保留")
            print("      如需完全清理映射，可以考虑重建索引")
        else:
            print("   🔄 需要继续清理文档中的非标准字段")

    except Exception as e:
        print(f"验证失败: {e}")


if __name__ == "__main__":
    final_verification()
