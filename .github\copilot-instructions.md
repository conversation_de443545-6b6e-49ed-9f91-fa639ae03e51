# 医疗采购文档智能解析项目

## 项目概述

该项目是一个医疗采购预算咨询管理资源库系统，主要功能是对招标公告和附件文档进行智能解析。系统支持处理 PDF、DOCX、DOC 等多种格式的文档，包括压缩包中的文档。

## 核心架构

### 1. 主要组件

```mermaid
graph TD
    A[ES数据源] -->|获取公告链接| B[文档下载器]
    B --> C[文档解析器]
    C --> D[LLM解析器]
    D --> E[智能融合器]
    E --> F[ES存储]
    B --> G[文件上传服务]
```

### 2. 关键文件

- `analyse_appendix.py`: 主处理逻辑
- `blacklist_manager.py`: 失败文档管理
- `es_deal.py`: ES操作封装
- `file_upload_service.py`: 文件上传服务

## 工作流程

### 1. 文档处理流程

#### Phase 1: 下载和上传
1. 从ES获取公告链接和附件链接
2. 下载所有附件文件
3. 上传文件到文件服务器
4. 填充 `appendix_info` 字段

#### Phase 2: 分析和融合
1. 分析文件类型（招标/合同/其他）
2. 智能融合处理
3. 结果存入ES

### 2. 智能融合流程

```python
主体解析结果 → 基础融合 → 识别空缺字段 → 字段分类 → 优先检索 → 跨文档检索 → 结果融合
```

## 关键约定

### 1. 字段优先级

- **合同相关字段** (优先从合同文件检索):
  ```python
  CONTRACT_FIELDS = [
      "bidder_price",
      "bidder_name", 
      "bidder_contact_person",
      "bidder_contact_phone_number",
      "bidder_contract_config_param"
  ]
  ```

- **其他字段** (优先从招标文件检索):
  - 项目信息、标的物信息等39个字段

### 2. 文档匹配规则

- 根据 `object_name` 进行文档匹配
- 相似度阈值默认为 0.5
- 使用关键词提取优化匹配:
  ```python
  # 移除修饰词
  设备、仪器、机器、系统、扫描、检测、监护、治疗
  
  # 保留核心关键词
  CT、MRI、超声、心电、血压、呼吸等
  ```

## 错误处理

### 1. 黑名单机制

- 文档处理失败3次后自动加入黑名单
- 通过 `blacklist_manager.py` 管理
- 支持手动管理:
  ```bash
  python analyse_appendix.py blacklist --action stats  # 查看统计
  python analyse_appendix.py blacklist --action list   # 列出黑名单
  python analyse_appendix.py blacklist --action remove --id "doc_id"  # 移除
  ```

### 2. 容错处理

- 文件上传失败不影响主流程
- LLM调用失败自动重试
- 支持文件类型降级解析

## 开发准则

### 1. 新增字段步骤

1. 在 `STANDARD_FIELDS` 中添加字段
2. 更新 `field_descriptions` 字典
3. 根据字段类型添加到相应的覆盖列表

### 2. 测试要求

必须确保以下测试通过:
- `test_full_workflow.py`: 完整工作流程
- `test_new_workflow_simple.py`: 两阶段处理
- `test_analyse_appendix_conditional_fields.py`: 条件字段

### 3. 性能优化

- 使用文件缓存避免重复下载
- 实现早期退出逻辑减少不必要处理
- LLM调用前进行长度检查

## 环境配置

### 1. 必需环境变量

```bash
ENABLE_FILE_UPLOAD=true  # 启用文件上传
```

### 2. 依赖项

```bash
pip install pdfplumber markitdown patool filetype docx2txt
```

### 3. ES配置

确保ES索引包含必要的字段映射，特别是:
- `appendix_info`: 数组类型
- 所有 `STANDARD_FIELDS` 中定义的字段

## 常见问题

1. **文档处理失败**
   - 检查黑名单统计
   - 验证文件下载是否成功
   - 确认LLM服务可用

2. **字段融合异常**
   - 检查 `object_name` 匹配逻辑
   - 确认字段优先级规则
   - 验证文档类型判断

3. **性能问题**
   - 使用批量处理
   - 启用早期退出逻辑
   - 优化文件缓存策略
