#!/usr/bin/env python3
"""
Elasticsearch chn_ylcg索引文档去重脚本

该脚本的功能：
1. 以url字段作为主要去重标识符
2. 优先级规则：
   - 当存在相同url的多个文档时，优先保留appendix字段不为空列表（非null且非空数组[]）的文档
   - 如果重复文档的appendix字段内容完全相同，则保留最后插入的文档（根据文档ID判断）
3. 实现方式：
   - 使用Elasticsearch聚合查询找出重复的url
   - 对每组重复文档应用上述优先级规则选择保留的文档
   - 删除不符合保留条件的重复文档
   - 提供--dry-run模式用于预览操作结果
   - 输出详细统计信息：总文档数、重复文档数、删除文档数、保留文档数
4. 安全措施：
   - 在删除前备份要删除的文档信息
   - 提供操作前后的对比报告
   - 记录详细的操作日志
"""

import argparse
import sys
import json
import csv
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
import os
from datetime import datetime

from es_deal import init_es_client, search_documents
from utils.log_cfg import log


def get_duplicate_urls(es_client, index_name: str) -> List[Dict]:
    """
    使用聚合查询找出重复的url

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        重复url的列表，每个元素包含url和文档数量
    """
    try:
        # 使用聚合查询找出重复的url
        agg_query = {
            "size": 0,
            "aggs": {
                "duplicate_urls": {
                    "terms": {
                        "field": "url.keyword",  # 使用keyword字段进行精确匹配
                        "min_doc_count": 2,  # 只返回至少有2个文档的url
                        "size": 10000,  # 最多返回10000个重复url
                    }
                }
            },
        }

        result = es_client.search(index=index_name, body=agg_query)

        duplicate_urls = []
        if result and result.get("aggregations", {}).get("duplicate_urls", {}).get(
            "buckets"
        ):
            for bucket in result["aggregations"]["duplicate_urls"]["buckets"]:
                duplicate_urls.append(
                    {"url": bucket["key"], "doc_count": bucket["doc_count"]}
                )

        log.info(f"找到 {len(duplicate_urls)} 个重复的url")
        return duplicate_urls

    except Exception as e:
        log.error(f"查询重复url失败: {e}")
        return []


def get_documents_by_url(es_client, index_name: str, url: str) -> List[Dict]:
    """
    根据url获取所有相关文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        url: 要查询的url

    Returns:
        文档列表
    """
    try:
        query = {
            "query": {"term": {"url.keyword": url}},
            "size": 100,  # 假设同一个url不会有超过100个文档
            "_source": ["url", "appendix", "_id"],
            "sort": [{"_id": {"order": "desc"}}],  # 按文档ID降序排列，最新的在前
        }

        result = search_documents(es_client, index_name, query=query)

        documents = []
        if result and result.get("hits", {}).get("hits"):
            for hit in result["hits"]["hits"]:
                source = hit["_source"]
                doc_info = {
                    "doc_id": hit["_id"],
                    "url": source.get("url"),
                    "appendix": source.get("appendix"),
                }
                documents.append(doc_info)

        return documents

    except Exception as e:
        log.error(f"根据url查询文档失败: {e}")
        return []


def is_valid_appendix(appendix: Any) -> bool:
    """
    检查appendix字段是否为有效的非空列表

    Args:
        appendix: appendix字段值

    Returns:
        bool: 如果是有效的非空列表则返回True
    """
    if appendix is None:
        return False

    if not isinstance(appendix, list):
        return False

    if len(appendix) == 0:
        return False

    return True


def select_document_to_keep(documents: List[Dict]) -> Dict:
    """
    根据优先级规则选择要保留的文档

    优先级规则：
    1. 优先保留appendix字段不为空列表的文档
    2. 如果appendix字段内容相同，保留最后插入的文档（文档ID最大的）

    Args:
        documents: 同一url的文档列表

    Returns:
        要保留的文档
    """
    if not documents:
        return None

    if len(documents) == 1:
        return documents[0]

    # 按appendix有效性分组
    valid_appendix_docs = []
    invalid_appendix_docs = []

    for doc in documents:
        if is_valid_appendix(doc["appendix"]):
            valid_appendix_docs.append(doc)
        else:
            invalid_appendix_docs.append(doc)

    # 优先选择有有效appendix的文档
    if valid_appendix_docs:
        # 如果有多个有效appendix的文档，选择文档ID最大的（最新的）
        return max(valid_appendix_docs, key=lambda x: x["doc_id"])
    else:
        # 如果都没有有效appendix，选择文档ID最大的（最新的）
        return max(invalid_appendix_docs, key=lambda x: x["doc_id"])


def backup_documents_to_delete(documents_to_delete: List[Dict], backup_file: str):
    """
    备份要删除的文档信息到CSV文件

    Args:
        documents_to_delete: 要删除的文档列表
        backup_file: 备份文件路径
    """
    try:
        with open(backup_file, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = ["doc_id", "url", "appendix", "delete_reason", "timestamp"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for doc in documents_to_delete:
                writer.writerow(
                    {
                        "doc_id": doc["doc_id"],
                        "url": doc["url"],
                        "appendix": (
                            json.dumps(doc["appendix"], ensure_ascii=False)
                            if doc["appendix"]
                            else ""
                        ),
                        "delete_reason": doc.get("delete_reason", ""),
                        "timestamp": datetime.now().isoformat(),
                    }
                )

        log.info(f"已备份 {len(documents_to_delete)} 个要删除的文档到: {backup_file}")

    except Exception as e:
        log.error(f"备份文档失败: {e}")


def bulk_delete_documents(es_client, index_name: str, doc_ids: List[str]):
    """
    批量删除文档

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        doc_ids: 要删除的文档ID列表

    Returns:
        删除结果统计
    """
    if not doc_ids:
        return {"success": 0, "failed": 0}

    try:
        # 构建批量删除请求
        body = []
        for doc_id in doc_ids:
            body.append({"delete": {"_index": index_name, "_id": doc_id}})

        # 执行批量删除
        response = es_client.bulk(body=body)

        # 检查结果
        success_count = 0
        error_count = 0

        for item in response["items"]:
            if "delete" in item:
                if item["delete"].get("status") in [
                    200,
                    404,
                ]:  # 200=删除成功, 404=文档不存在
                    success_count += 1
                else:
                    error_count += 1
                    log.error(f"删除失败: {item['delete']}")

        log.info(f"批量删除完成 - 成功: {success_count}, 失败: {error_count}")
        return {"success": success_count, "failed": error_count}

    except Exception as e:
        log.error(f"批量删除失败: {e}")
        return {"success": 0, "failed": len(doc_ids)}


def get_total_documents_count(es_client, index_name: str) -> int:
    """
    获取索引中的文档总数

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称

    Returns:
        文档总数
    """
    try:
        result = es_client.count(index=index_name)
        return result.get("count", 0)
    except Exception as e:
        log.error(f"获取文档总数失败: {e}")
        return 0


def process_deduplication(
    es_client, index_name: str, batch_size: int = 100, dry_run: bool = False
):
    """
    处理文档去重

    Args:
        es_client: Elasticsearch客户端
        index_name: 索引名称
        batch_size: 批处理大小
        dry_run: 是否为试运行模式
    """
    try:
        log.info(f"开始处理文档去重...")
        log.info(f"索引: {index_name}")
        log.info(f"批处理大小: {batch_size}")
        log.info(f"试运行模式: {dry_run}")

        # 获取总文档数
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(f"索引中文档总数: {total_docs}")

        if total_docs == 0:
            log.info("索引中没有文档")
            return

        # 查找重复的url
        duplicate_urls = get_duplicate_urls(es_client, index_name)

        if not duplicate_urls:
            log.info("没有找到重复的url")
            return

        log.info(f"找到 {len(duplicate_urls)} 个重复的url")

        # 统计信息
        stats = {
            "total_docs": total_docs,
            "duplicate_urls": len(duplicate_urls),
            "total_duplicate_docs": 0,
            "docs_to_delete": 0,
            "docs_to_keep": 0,
            "deleted_success": 0,
            "deleted_failed": 0,
        }

        # 准备备份文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"deleted_documents_backup_{timestamp}.csv"

        documents_to_delete = []
        documents_to_keep = []

        # 处理每个重复的url
        for i, url_info in enumerate(duplicate_urls, 1):
            url = url_info["url"]
            doc_count = url_info["doc_count"]

            log.info(
                f"[{i}/{len(duplicate_urls)}] 处理重复url: {url} (共{doc_count}个文档)"
            )

            # 获取该url的所有文档
            documents = get_documents_by_url(es_client, index_name, url)

            if len(documents) != doc_count:
                log.warning(
                    f"文档数量不匹配: 聚合结果={doc_count}, 实际查询={len(documents)}"
                )

            stats["total_duplicate_docs"] += len(documents)

            # 选择要保留的文档
            doc_to_keep = select_document_to_keep(documents)

            if doc_to_keep:
                documents_to_keep.append(doc_to_keep)

                # 标记其他文档为待删除
                for doc in documents:
                    if doc["doc_id"] != doc_to_keep["doc_id"]:
                        doc["delete_reason"] = (
                            f"重复url，保留文档ID: {doc_to_keep['doc_id']}"
                        )
                        documents_to_delete.append(doc)

                log.info(
                    f"  保留文档: {doc_to_keep['doc_id']} (appendix有效: {is_valid_appendix(doc_to_keep['appendix'])})"
                )
                log.info(
                    f"  删除文档: {[doc['doc_id'] for doc in documents if doc['doc_id'] != doc_to_keep['doc_id']]}"
                )

        stats["docs_to_delete"] = len(documents_to_delete)
        stats["docs_to_keep"] = len(documents_to_keep)

        # 输出统计信息
        log.info("=" * 60)
        log.info("去重统计:")
        log.info(f"  索引文档总数: {stats['total_docs']}")
        log.info(f"  重复url数量: {stats['duplicate_urls']}")
        log.info(f"  重复文档总数: {stats['total_duplicate_docs']}")
        log.info(f"  计划保留文档: {stats['docs_to_keep']}")
        log.info(f"  计划删除文档: {stats['docs_to_delete']}")
        log.info("=" * 60)

        if documents_to_delete:
            # 备份要删除的文档
            backup_documents_to_delete(documents_to_delete, backup_file)

            if not dry_run:
                # 执行删除操作
                doc_ids_to_delete = [doc["doc_id"] for doc in documents_to_delete]

                # 分批删除
                batch_start = 0
                while batch_start < len(doc_ids_to_delete):
                    batch_end = min(batch_start + batch_size, len(doc_ids_to_delete))
                    batch_ids = doc_ids_to_delete[batch_start:batch_end]

                    log.info(
                        f"删除第 {batch_start//batch_size + 1} 批次: {len(batch_ids)} 个文档"
                    )

                    result = bulk_delete_documents(es_client, index_name, batch_ids)
                    stats["deleted_success"] += result["success"]
                    stats["deleted_failed"] += result["failed"]

                    batch_start = batch_end

                log.info(
                    f"删除完成 - 成功: {stats['deleted_success']}, 失败: {stats['deleted_failed']}"
                )

                # 验证删除结果
                final_total_docs = get_total_documents_count(es_client, index_name)
                expected_docs = stats["total_docs"] - stats["deleted_success"]

                log.info("=" * 60)
                log.info("删除结果验证:")
                log.info(f"  删除前文档数: {stats['total_docs']}")
                log.info(f"  成功删除数: {stats['deleted_success']}")
                log.info(f"  预期剩余数: {expected_docs}")
                log.info(f"  实际剩余数: {final_total_docs}")
                log.info(
                    f"  数据一致性: {'✓' if final_total_docs == expected_docs else '✗'}"
                )
                log.info("=" * 60)

            else:
                log.info("试运行模式，未执行实际删除")
        else:
            log.info("没有需要删除的文档")

    except Exception as e:
        log.error(f"处理去重过程中发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Elasticsearch chn_ylcg索引文档去重：以url字段为标识符，优先保留appendix非空的文档"
    )
    parser.add_argument(
        "--index", type=str, default="chn_ylcg", help="目标索引名称（默认chn_ylcg）"
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="批处理大小（默认100）"
    )
    parser.add_argument(
        "--dry-run", action="store_true", help="试运行模式，不执行实际删除"
    )

    args = parser.parse_args()

    try:
        # 加载环境变量
        load_dotenv()

        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()

        log.info(f"目标索引: {args.index}")
        log.info(f"批处理大小: {args.batch_size}")
        log.info(f"试运行模式: {args.dry_run}")

        # 检查索引是否存在
        if not es.indices.exists(index=args.index):
            log.error(f"索引 {args.index} 不存在")
            sys.exit(1)

        # 处理去重
        process_deduplication(es, args.index, args.batch_size, args.dry_run)

    except KeyboardInterrupt:
        log.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
