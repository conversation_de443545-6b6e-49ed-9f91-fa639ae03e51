#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Elasticsearch 字段映射更新脚本

该脚本用于将 markersweb_attachment_analysis_alias 索引中的 insert_time 字段
从 text 类型转换为 date 类型。

注意：由于 ES 不支持直接修改已存在字段的类型，需要通过重建索引的方式实现。
"""

import json
import logging
import requests
from datetime import datetime
from requests.auth import HTTPBasicAuth
import urllib3

# 禁用 InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ES 配置
ES_CONFIG = {
    "HOST": "http://***********:9200",
    "USER": "elastic",
    "PASS": "elastic",
    "INDEX_ALIAS": "markersweb_attachment_analysis_alias",
    "TIMEOUT": 120,
}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("update_mapping.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)


def get_current_mapping(host: str, auth: HTTPBasicAuth, index_name: str) -> dict:
    """获取当前索引的映射"""
    try:
        response = requests.get(
            f"{host}/{index_name}/_mapping",
            auth=auth,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logging.error(f"获取索引映射失败: {e}")
        return {}


def create_new_mapping(original_mapping: dict) -> dict:
    """创建新的映射，将 insert_time 字段类型改为 date"""
    new_mapping = json.loads(json.dumps(original_mapping))  # 深拷贝

    # 遍历所有索引的映射
    for index_name, index_mapping in new_mapping.items():
        properties = index_mapping.get("mappings", {}).get("properties", {})

        if "insert_time" in properties:
            # 将 insert_time 字段类型改为 date
            properties["insert_time"] = {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis",
            }
            logging.info(f"已更新索引 {index_name} 的 insert_time 字段映射为 date 类型")

    return new_mapping


def rebuild_index_for_insert_time(
    host: str, auth: HTTPBasicAuth, old_index: str, new_index: str
) -> bool:
    """专门为 insert_time 字段重建索引"""
    try:
        logging.info(f"准备重建索引 {old_index}，修改 insert_time 字段类型为 date")

        # 1. 获取原mapping和settings
        response = requests.get(f"{host}/{old_index}/_mapping", auth=auth, verify=False)
        response.raise_for_status()
        mapping_data = response.json()
        mapping = mapping_data[old_index]["mappings"]

        response = requests.get(
            f"{host}/{old_index}/_settings", auth=auth, verify=False
        )
        response.raise_for_status()
        settings_data = response.json()
        settings = settings_data[old_index]["settings"]["index"]

        # 2. 修改 insert_time 字段映射
        if "properties" in mapping and "insert_time" in mapping["properties"]:
            mapping["properties"]["insert_time"] = {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis",
            }
            logging.info("已更新 insert_time 字段映射为 date 类型")

        # 3. 过滤设置
        filtered_settings = {}
        for k, v in settings.items():
            if not k.startswith(("version", "uuid", "creation_date", "provided_name")):
                filtered_settings[k] = v

        # 4. 创建新索引
        body = {
            "settings": filtered_settings,
            "mappings": mapping,
        }

        response = requests.put(
            f"{host}/{new_index}",
            auth=auth,
            json=body,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        logging.info(f"新索引 {new_index} 创建成功")

        # 5. reindex数据，处理 insert_time 字段
        reindex_body = {
            "source": {"index": old_index},
            "dest": {"index": new_index},
            "script": {
                "lang": "painless",
                "source": """
                    // 处理 insert_time 字段
                    if (ctx._source.insert_time == null || ctx._source.insert_time == '') {
                        // 如果字段为空，设置为当前时间
                        ctx._source.insert_time = '2025-07-28 09:30:00';
                    }
                    // 如果字段有值，保持不变，ES会自动解析为date类型
                """,
            },
        }

        response = requests.post(
            f"{host}/_reindex",
            auth=auth,
            json=reindex_body,
            timeout=ES_CONFIG["TIMEOUT"] * 10,
            verify=False,
        )

        if response.status_code != 200:
            logging.error(f"Reindex请求失败，状态码: {response.status_code}")
            logging.error(f"错误响应: {response.text}")
            return False

        response.raise_for_status()

        result = response.json()
        logging.info(f"数据reindex到 {new_index} 完成")
        return True

    except Exception as e:
        logging.error(f"重建索引失败: {e}")
        if hasattr(e, "response") and e.response:
            logging.error(f"错误详情: {e.response.text}")
        return False


def update_alias(
    host: str, auth: HTTPBasicAuth, old_index: str, new_index: str, alias: str
) -> bool:
    """更新别名指向"""
    alias_body = {
        "actions": [
            {"remove": {"index": old_index, "alias": alias}},
            {"add": {"index": new_index, "alias": alias}},
        ]
    }

    try:
        response = requests.post(
            f"{host}/_aliases",
            auth=auth,
            json=alias_body,
            timeout=ES_CONFIG["TIMEOUT"],
            verify=False,
        )
        response.raise_for_status()
        logging.info(f"别名 {alias} 已从 {old_index} 切换到 {new_index}")
        return True

    except Exception as e:
        logging.error(f"更新别名失败: {e}")
        return False


def main():
    """主函数"""
    host = ES_CONFIG["HOST"]
    auth = HTTPBasicAuth(ES_CONFIG["USER"], ES_CONFIG["PASS"])
    alias = ES_CONFIG["INDEX_ALIAS"]

    logging.info("=" * 60)
    logging.info("开始 ES 字段映射更新任务")
    logging.info("=" * 60)

    # 1. 获取当前别名指向的索引
    try:
        response = requests.get(f"{host}/_alias/{alias}", auth=auth, verify=False)
        response.raise_for_status()
        alias_info = response.json()

        if not alias_info:
            logging.error(f"别名 {alias} 不存在")
            return

        # 获取当前索引名
        current_indices = list(alias_info.keys())
        if not current_indices:
            logging.error(f"别名 {alias} 没有指向任何索引")
            return

        current_index = current_indices[0]  # 假设别名只指向一个索引
        logging.info(f"当前别名 {alias} 指向索引: {current_index}")

    except Exception as e:
        logging.error(f"获取别名信息失败: {e}")
        return

    # 2. 获取当前映射
    current_mapping = get_current_mapping(host, auth, current_index)
    if not current_mapping:
        logging.error("获取当前映射失败")
        return

    # 3. 检查 insert_time 字段类型
    index_mapping = current_mapping.get(current_index, {})
    properties = index_mapping.get("mappings", {}).get("properties", {})
    insert_time_mapping = properties.get("insert_time", {})
    current_type = insert_time_mapping.get("type", "unknown")

    logging.info(f"当前 insert_time 字段类型: {current_type}")

    if current_type == "date":
        logging.info("insert_time 字段已经是 date 类型，无需更新")
        return

    # 4. 创建新索引名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_index = f"{current_index}_date_{timestamp}"

    # 5. 创建新映射
    new_mapping = create_new_mapping(current_mapping)
    new_index_mapping = new_mapping[current_index]["mappings"]

    # 6. 重建索引并转换数据
    if not rebuild_index_for_insert_time(host, auth, current_index, new_index):
        logging.error("重建索引失败")
        return

    # 8. 验证新索引数据
    try:
        response = requests.get(f"{host}/{new_index}/_count", auth=auth, verify=False)
        response.raise_for_status()
        new_count = response.json()["count"]

        response = requests.get(
            f"{host}/{current_index}/_count", auth=auth, verify=False
        )
        response.raise_for_status()
        old_count = response.json()["count"]

        logging.info(f"数据验证: 原索引 {old_count} 条，新索引 {new_count} 条")

        if new_count != old_count:
            logging.warning("新旧索引数据量不一致，请检查")

    except Exception as e:
        logging.error(f"数据验证失败: {e}")

    # 9. 更新别名
    if update_alias(host, auth, current_index, new_index, alias):
        logging.info("别名更新成功")

        # 10. 可选：删除旧索引（谨慎操作）
        confirm = input(f"是否删除旧索引 {current_index}? (yes/no): ")
        if confirm.lower() == "yes":
            try:
                response = requests.delete(
                    f"{host}/{current_index}", auth=auth, verify=False
                )
                response.raise_for_status()
                logging.info(f"旧索引 {current_index} 已删除")
            except Exception as e:
                logging.error(f"删除旧索引失败: {e}")
        else:
            logging.info(f"保留旧索引 {current_index}")

    logging.info("=" * 60)
    logging.info("ES 字段映射更新任务完成")
    logging.info("=" * 60)


if __name__ == "__main__":
    main()
