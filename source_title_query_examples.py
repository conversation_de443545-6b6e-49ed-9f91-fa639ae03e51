#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
source_title 字段查询示例

展示如何使用 source_title 字段的 text 和 keyword 子字段进行不同类型的查询。
"""

import os
from dotenv import load_dotenv
from es_deal import init_es_client
from utils.log_cfg import log


def demo_text_vs_keyword_queries():
    """演示 text 字段和 keyword 子字段的查询差异"""
    
    load_dotenv()
    es = init_es_client()
    alias_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias")
    
    print("=" * 80)
    print("source_title 字段查询示例演示")
    print("=" * 80)
    
    # 1. 全文搜索 (match 查询 - 使用 text 字段)
    print("\n1. 全文搜索 (分词匹配) - 使用 source_title")
    print("-" * 50)
    
    match_query = {
        "query": {
            "match": {
                "source_title": "医疗设备采购"
            }
        },
        "size": 5,
        "_source": ["source_title", "source_url"]
    }
    
    try:
        response = es.search(index=alias_name, body=match_query)
        total_hits = response['hits']['total']['value']
        print(f"查询: source_title 包含 '医疗设备采购' (分词匹配)")
        print(f"结果数量: {total_hits}")
        
        for hit in response['hits']['hits'][:3]:
            title = hit['_source'].get('source_title', 'N/A')
            print(f"  - {title[:80]}...")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 2. 短语匹配 (match_phrase 查询)
    print("\n2. 短语匹配 - 使用 source_title")
    print("-" * 50)
    
    phrase_query = {
        "query": {
            "match_phrase": {
                "source_title": "医疗设备采购"
            }
        },
        "size": 5,
        "_source": ["source_title"]
    }
    
    try:
        response = es.search(index=alias_name, body=phrase_query)
        total_hits = response['hits']['total']['value']
        print(f"查询: source_title 包含短语 '医疗设备采购'")
        print(f"结果数量: {total_hits}")
        
        for hit in response['hits']['hits'][:3]:
            title = hit['_source'].get('source_title', 'N/A')
            print(f"  - {title[:80]}...")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 3. 精准匹配 (term 查询 - 使用 keyword 子字段)
    print("\n3. 精准匹配 - 使用 source_title.keyword")
    print("-" * 50)
    
    # 先获取一个实际的标题用于精准匹配演示
    sample_query = {
        "query": {"match_all": {}},
        "size": 1,
        "_source": ["source_title"]
    }
    
    try:
        sample_response = es.search(index=alias_name, body=sample_query)
        if sample_response['hits']['hits']:
            sample_title = sample_response['hits']['hits'][0]['_source']['source_title']
            
            exact_query = {
                "query": {
                    "term": {
                        "source_title.keyword": sample_title
                    }
                },
                "size": 5,
                "_source": ["source_title"]
            }
            
            response = es.search(index=alias_name, body=exact_query)
            total_hits = response['hits']['total']['value']
            print(f"查询: source_title.keyword 精确等于")
            print(f"  '{sample_title[:60]}...'")
            print(f"结果数量: {total_hits}")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 4. 前缀匹配 (prefix 查询 - 使用 keyword 子字段)
    print("\n4. 前缀匹配 - 使用 source_title.keyword")
    print("-" * 50)
    
    prefix_query = {
        "query": {
            "prefix": {
                "source_title.keyword": "浙江省"
            }
        },
        "size": 5,
        "_source": ["source_title"]
    }
    
    try:
        response = es.search(index=alias_name, body=prefix_query)
        total_hits = response['hits']['total']['value']
        print(f"查询: source_title.keyword 以 '浙江省' 开头")
        print(f"结果数量: {total_hits}")
        
        for hit in response['hits']['hits'][:3]:
            title = hit['_source'].get('source_title', 'N/A')
            print(f"  - {title[:80]}...")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 5. 通配符匹配 (wildcard 查询 - 使用 keyword 子字段)
    print("\n5. 通配符匹配 - 使用 source_title.keyword")
    print("-" * 50)
    
    wildcard_query = {
        "query": {
            "wildcard": {
                "source_title.keyword": "*医院*采购*"
            }
        },
        "size": 5,
        "_source": ["source_title"]
    }
    
    try:
        response = es.search(index=alias_name, body=wildcard_query)
        total_hits = response['hits']['total']['value']
        print(f"查询: source_title.keyword 匹配模式 '*医院*采购*'")
        print(f"结果数量: {total_hits}")
        
        for hit in response['hits']['hits'][:3]:
            title = hit['_source'].get('source_title', 'N/A')
            print(f"  - {title[:80]}...")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    # 6. 聚合统计 - 使用 keyword 子字段
    print("\n6. 聚合统计 - 使用 source_title.keyword")
    print("-" * 50)
    
    agg_query = {
        "aggs": {
            "title_terms": {
                "terms": {
                    "field": "source_title.keyword",
                    "size": 10
                }
            }
        },
        "size": 0
    }
    
    try:
        response = es.search(index=alias_name, body=agg_query)
        buckets = response.get("aggregations", {}).get("title_terms", {}).get("buckets", [])
        print(f"聚合: 按 source_title.keyword 分组统计")
        print(f"聚合桶数量: {len(buckets)}")
        
        for i, bucket in enumerate(buckets[:5]):
            title = bucket['key']
            count = bucket['doc_count']
            print(f"  {i+1}. {title[:60]}... (出现 {count} 次)")
            
    except Exception as e:
        print(f"聚合查询失败: {e}")
    
    # 7. 复合查询示例
    print("\n7. 复合查询 - 结合 text 和 keyword")
    print("-" * 50)
    
    complex_query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "source_title": "医疗设备"
                        }
                    }
                ],
                "filter": [
                    {
                        "prefix": {
                            "source_title.keyword": "浙江"
                        }
                    }
                ]
            }
        },
        "size": 5,
        "_source": ["source_title"]
    }
    
    try:
        response = es.search(index=alias_name, body=complex_query)
        total_hits = response['hits']['total']['value']
        print(f"查询: 包含'医疗设备' 且以'浙江'开头")
        print(f"结果数量: {total_hits}")
        
        for hit in response['hits']['hits'][:3]:
            title = hit['_source'].get('source_title', 'N/A')
            print(f"  - {title[:80]}...")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    print("\n" + "=" * 80)
    print("查询示例演示完成")
    print("=" * 80)


def show_query_templates():
    """显示常用查询模板"""
    
    print("\n" + "=" * 80)
    print("常用查询模板")
    print("=" * 80)
    
    templates = [
        {
            "name": "全文搜索 (分词匹配)",
            "description": "搜索包含指定词汇的文档，支持分词",
            "query": {
                "query": {
                    "match": {
                        "source_title": "搜索关键词"
                    }
                }
            }
        },
        {
            "name": "短语匹配",
            "description": "搜索包含完整短语的文档",
            "query": {
                "query": {
                    "match_phrase": {
                        "source_title": "完整短语"
                    }
                }
            }
        },
        {
            "name": "精准匹配",
            "description": "精确匹配完整标题",
            "query": {
                "query": {
                    "term": {
                        "source_title.keyword": "完整标题文本"
                    }
                }
            }
        },
        {
            "name": "前缀匹配",
            "description": "匹配以指定文本开头的标题",
            "query": {
                "query": {
                    "prefix": {
                        "source_title.keyword": "前缀文本"
                    }
                }
            }
        },
        {
            "name": "通配符匹配",
            "description": "使用通配符模式匹配",
            "query": {
                "query": {
                    "wildcard": {
                        "source_title.keyword": "*模式*匹配*"
                    }
                }
            }
        },
        {
            "name": "标题聚合统计",
            "description": "按标题分组统计文档数量",
            "query": {
                "aggs": {
                    "title_stats": {
                        "terms": {
                            "field": "source_title.keyword",
                            "size": 20
                        }
                    }
                },
                "size": 0
            }
        }
    ]
    
    for i, template in enumerate(templates, 1):
        print(f"\n{i}. {template['name']}")
        print(f"   描述: {template['description']}")
        print(f"   查询:")
        import json
        print(json.dumps(template['query'], indent=4, ensure_ascii=False))


if __name__ == "__main__":
    # 运行查询演示
    demo_text_vs_keyword_queries()
    
    # 显示查询模板
    show_query_templates()
