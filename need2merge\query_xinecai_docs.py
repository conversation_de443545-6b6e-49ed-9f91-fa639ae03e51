#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 定义查询
query = {
    "query": {
        "bool": {
            "must": [
                {"prefix": {"source_url": "http://www.xinecai.com"}},
                {"term": {"source_category": "004"}},
            ]
        }
    },
    "size": 0,  # 只需要计数，不需要返回文档内容
}

# 执行查询
index_name = os.getenv("ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_v3")
print(f"使用索引: {index_name}")
print(
    f"查询条件: source_url 以 'https://www.xinecai.com' 开头且 source_category 等于 '004'"
)

try:
    response = es.search(index=index_name, body=query)

    # 获取匹配的文档数量
    count = response["hits"]["total"]["value"]
    print(f"符合条件的文档数量: {count}")
except Exception as e:
    print(f"查询执行失败: {str(e)}")
    exit(1)
