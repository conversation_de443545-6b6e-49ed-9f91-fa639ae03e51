#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
import re
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引名称
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_alias")
print(f"使用索引: {index_name}")

# 定义查询 - 查找prj_name为空且source_url以https://www.xinecai.com开头的文档
query = {
    "query": {
        "bool": {
            "must": [
                {"match_phrase_prefix": {"source_url": "https://www.xinecai.com"}},
                {
                    "bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "prj_name"}}}},
                            {"term": {"prj_name": ""}},
                        ],
                        "minimum_should_match": 1,
                    }
                },
                {"exists": {"field": "source_title"}},  # 确保source_title字段存在
            ]
        }
    },
    "size": 100,  # 处理更多文档
    "_source": ["source_url", "source_title", "prj_name"],
}

# 定义正则表达式模式
pattern1 = r"(中国科大附一院（安徽省立医院）.*?项目)"
pattern2 = r"(中国科学技术大学附属第一医院（安徽省立医院）.*?项目)"
pattern3 = r"(中国科大附一院（安徽省立医院）.*?)招标公告"
pattern4 = r"(中国科学技术大学附属第一医院（安徽省立医院）.*?)入围结果公告"
pattern5 = r"(中国科大附一院（安徽省立医院）.*?)比选采购信息"

try:
    # 执行查询
    response = es.search(index=index_name, body=query)

    hits = response["hits"]["hits"]
    total_hits = response["hits"]["total"]["value"]

    print(f"找到符合条件的文档总数: {total_hits}")
    print(f"本次分析将处理前 {len(hits)} 个文档")

    # 分类存储
    matched_docs = []
    unmatched_docs = []

    # 处理文档
    for hit in hits:
        doc_id = hit["_id"]
        source = hit["_source"]
        source_title = source.get("source_title", "").strip().replace(" ", "")

        # 应用正则表达式
        match = (
            re.search(pattern1, source_title)
            or re.search(pattern2, source_title)
            or re.search(pattern3, source_title)
            or re.search(pattern4, source_title)
            or re.search(pattern5, source_title)
        )

        if match:
            matched_docs.append((doc_id, source_title, match.group(1)))
        else:
            unmatched_docs.append((doc_id, source_title))

    # 显示统计信息
    print(f"\n匹配成功文档数: {len(matched_docs)}")
    print(f"未匹配文档数: {len(unmatched_docs)}")

    # 显示未匹配的文档
    if unmatched_docs:
        print("\n=== 未匹配文档详情 ===")
        for doc_id, title in unmatched_docs:
            print(f"\n未匹配文档 ID: {doc_id}")
            print(f"标题: {title}")

            # 检查标题中是否包含关键词
            if "中国科大附一院" in title:
                print("包含'中国科大附一院'但未匹配")
            elif "中国科学技术大学附属第一医院" in title:
                print("包含'中国科学技术大学附属第一医院'但未匹配")

            if "项目" in title:
                print("包含'项目'但未匹配")
            else:
                print("不包含'项目'关键词")
    else:
        print("\n所有文档都匹配成功！")

    # 显示一些匹配成功的示例
    if matched_docs:
        print("\n=== 匹配成功示例 ===")
        for i, (doc_id, title, project_name) in enumerate(matched_docs[:3]):
            print(f"\n示例 {i+1}:")
            print(f"文档 ID: {doc_id}")
            print(f"原始标题: {title}")
            print(f"提取的项目名: {project_name}")

except Exception as e:
    print(f"处理过程中出错: {str(e)}")
    exit(1)
