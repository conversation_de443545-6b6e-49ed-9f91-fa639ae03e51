#!/usr/bin/env python3
"""
测试chn_ylcg索引去重脚本的功能
"""

import sys
import json
from dotenv import load_dotenv

from es_deal import init_es_client
from deduplicate_chn_ylcg import (
    get_duplicate_urls,
    get_documents_by_url,
    is_valid_appendix,
    select_document_to_keep,
    get_total_documents_count
)
from utils.log_cfg import log


def test_is_valid_appendix():
    """测试appendix字段有效性检查"""
    log.info("测试appendix字段有效性检查...")
    
    # 测试用例
    test_cases = [
        (None, False, "null值"),
        ([], False, "空数组"),
        ("not_a_list", False, "非数组类型"),
        ([{"name": "附件1"}], True, "有效的非空数组"),
        ([1, 2, 3], True, "非空数组"),
        (0, False, "数字0"),
        ("", False, "空字符串"),
    ]
    
    for appendix, expected, description in test_cases:
        result = is_valid_appendix(appendix)
        status = "✓" if result == expected else "✗"
        log.info(f"  {status} {description}: {appendix} -> {result}")
        
        if result != expected:
            log.error(f"测试失败: 期望 {expected}, 实际 {result}")


def test_select_document_to_keep():
    """测试文档选择逻辑"""
    log.info("测试文档选择逻辑...")
    
    # 测试用例1: 有appendix vs 无appendix
    documents1 = [
        {"doc_id": "doc1", "appendix": None},
        {"doc_id": "doc2", "appendix": [{"name": "附件1"}]},
        {"doc_id": "doc3", "appendix": []},
    ]
    
    result1 = select_document_to_keep(documents1)
    expected1 = "doc2"
    status1 = "✓" if result1["doc_id"] == expected1 else "✗"
    log.info(f"  {status1} 测试1 - 优先选择有appendix的文档: 选择了 {result1['doc_id']} (期望 {expected1})")
    
    # 测试用例2: 都有appendix，选择ID最大的
    documents2 = [
        {"doc_id": "doc1", "appendix": [{"name": "附件1"}]},
        {"doc_id": "doc3", "appendix": [{"name": "附件2"}]},
        {"doc_id": "doc2", "appendix": [{"name": "附件3"}]},
    ]
    
    result2 = select_document_to_keep(documents2)
    expected2 = "doc3"
    status2 = "✓" if result2["doc_id"] == expected2 else "✗"
    log.info(f"  {status2} 测试2 - 都有appendix时选择ID最大的: 选择了 {result2['doc_id']} (期望 {expected2})")
    
    # 测试用例3: 都没有appendix，选择ID最大的
    documents3 = [
        {"doc_id": "doc1", "appendix": None},
        {"doc_id": "doc3", "appendix": []},
        {"doc_id": "doc2", "appendix": None},
    ]
    
    result3 = select_document_to_keep(documents3)
    expected3 = "doc3"
    status3 = "✓" if result3["doc_id"] == expected3 else "✗"
    log.info(f"  {status3} 测试3 - 都没有appendix时选择ID最大的: 选择了 {result3['doc_id']} (期望 {expected3})")


def test_real_data_analysis(es_client, index_name: str):
    """分析真实数据中的重复情况"""
    log.info(f"分析 {index_name} 索引中的重复数据...")
    
    try:
        # 获取总文档数
        total_docs = get_total_documents_count(es_client, index_name)
        log.info(f"索引总文档数: {total_docs}")
        
        # 查找重复的url
        duplicate_urls = get_duplicate_urls(es_client, index_name)
        log.info(f"重复url数量: {len(duplicate_urls)}")
        
        if not duplicate_urls:
            log.info("没有找到重复的url")
            return
        
        # 分析前几个重复url的详细情况
        sample_size = min(5, len(duplicate_urls))
        log.info(f"分析前 {sample_size} 个重复url的详细情况:")
        
        total_duplicate_docs = 0
        total_docs_to_delete = 0
        
        for i, url_info in enumerate(duplicate_urls[:sample_size]):
            url = url_info["url"]
            doc_count = url_info["doc_count"]
            
            log.info(f"\n[{i+1}] URL: {url}")
            log.info(f"    文档数量: {doc_count}")
            
            # 获取该url的所有文档
            documents = get_documents_by_url(es_client, index_name, url)
            total_duplicate_docs += len(documents)
            
            # 分析appendix字段情况
            valid_appendix_count = sum(1 for doc in documents if is_valid_appendix(doc["appendix"]))
            invalid_appendix_count = len(documents) - valid_appendix_count
            
            log.info(f"    有效appendix文档: {valid_appendix_count}")
            log.info(f"    无效appendix文档: {invalid_appendix_count}")
            
            # 选择要保留的文档
            doc_to_keep = select_document_to_keep(documents)
            docs_to_delete = len(documents) - 1
            total_docs_to_delete += docs_to_delete
            
            log.info(f"    保留文档: {doc_to_keep['doc_id']} (appendix有效: {is_valid_appendix(doc_to_keep['appendix'])})")
            log.info(f"    删除文档数: {docs_to_delete}")
            
            # 显示每个文档的详细信息
            for j, doc in enumerate(documents):
                appendix_status = "有效" if is_valid_appendix(doc["appendix"]) else "无效"
                keep_status = "保留" if doc["doc_id"] == doc_to_keep["doc_id"] else "删除"
                log.info(f"      文档{j+1}: {doc['doc_id']} - appendix: {appendix_status} - 操作: {keep_status}")
        
        # 估算全部重复数据的情况
        if len(duplicate_urls) > sample_size:
            avg_docs_per_url = total_duplicate_docs / sample_size
            estimated_total_duplicate_docs = int(avg_docs_per_url * len(duplicate_urls))
            estimated_total_docs_to_delete = estimated_total_duplicate_docs - len(duplicate_urls)
            
            log.info(f"\n估算全部重复数据情况:")
            log.info(f"  估算重复文档总数: {estimated_total_duplicate_docs}")
            log.info(f"  估算删除文档数: {estimated_total_docs_to_delete}")
            log.info(f"  估算删除比例: {estimated_total_docs_to_delete/total_docs*100:.2f}%")
        
    except Exception as e:
        log.error(f"分析真实数据失败: {e}")


def test_query_performance(es_client, index_name: str):
    """测试查询性能"""
    log.info("测试查询性能...")
    
    try:
        import time
        
        # 测试聚合查询性能
        start_time = time.time()
        duplicate_urls = get_duplicate_urls(es_client, index_name)
        agg_time = time.time() - start_time
        
        log.info(f"聚合查询耗时: {agg_time:.2f}秒")
        log.info(f"找到重复url数量: {len(duplicate_urls)}")
        
        if duplicate_urls:
            # 测试单个url查询性能
            sample_url = duplicate_urls[0]["url"]
            
            start_time = time.time()
            documents = get_documents_by_url(es_client, index_name, sample_url)
            query_time = time.time() - start_time
            
            log.info(f"单个url查询耗时: {query_time:.3f}秒")
            log.info(f"返回文档数量: {len(documents)}")
        
    except Exception as e:
        log.error(f"性能测试失败: {e}")


def main():
    """主函数"""
    try:
        # 加载环境变量
        load_dotenv()
        
        # 初始化ES客户端
        log.info("正在初始化Elasticsearch客户端...")
        es = init_es_client()
        
        index_name = "chn_ylcg"
        
        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            log.error(f"索引 {index_name} 不存在")
            sys.exit(1)
        
        log.info("=" * 60)
        log.info("开始测试去重脚本功能")
        log.info("=" * 60)
        
        # 1. 测试工具函数
        test_is_valid_appendix()
        log.info("")
        
        test_select_document_to_keep()
        log.info("")
        
        # 2. 分析真实数据
        test_real_data_analysis(es, index_name)
        log.info("")
        
        # 3. 性能测试
        test_query_performance(es, index_name)
        
        log.info("=" * 60)
        log.info("测试完成")
        log.info("=" * 60)
        
    except KeyboardInterrupt:
        log.info("用户中断测试")
        sys.exit(0)
    except Exception as e:
        log.error(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
