#!/usr/bin/env python3
"""
测试 datetime JSON 序列化修复
"""

import json
from datetime import datetime
import sys
import os

# 添加当前目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_prepare_for_json_logging():
    """测试 prepare_for_json_logging 函数"""

    # 从 analyse_appendix.py 导入函数
    try:
        from analyse_appendix import prepare_for_json_logging

        print("✓ 成功从 analyse_appendix.py 导入 prepare_for_json_logging")
    except ImportError as e:
        print(f"✗ 从 analyse_appendix.py 导入失败: {e}")
        return False

    # 从 analyse_appendix_asc.py 导入函数
    try:
        from analyse_appendix_asc import prepare_for_json_logging as prepare_asc

        print("✓ 成功从 analyse_appendix_asc.py 导入 prepare_for_json_logging")
    except ImportError as e:
        print(f"✗ 从 analyse_appendix_asc.py 导入失败: {e}")
        return False

    # 从 analyse_noappendix.py 导入函数
    try:
        from analyse_noappendix import prepare_for_json_logging as prepare_no

        print("✓ 成功从 analyse_noappendix.py 导入 prepare_for_json_logging")
    except ImportError as e:
        print(f"✗ 从 analyse_noappendix.py 导入失败: {e}")
        return False

    # 测试数据
    test_data = {
        "source_id": "test_123",
        "source_title": "测试文档",
        "source_create_time": datetime.now(),
        "release_time": datetime(2024, 1, 15, 10, 30, 0),
        "insert_time": datetime.now(),
        "object_name": "测试设备",
        "object_price": 1000.0,
        "nested_data": {
            "created_at": datetime.now(),
            "updated_at": datetime(2023, 12, 1, 14, 20, 30),
        },
        "list_data": [{"timestamp": datetime.now()}, {"value": "normal_string"}],
    }

    print("\n" + "=" * 50)
    print("测试原始数据（包含 datetime 对象）:")
    print("=" * 50)

    # 尝试直接序列化（应该失败）
    try:
        json.dumps(test_data, ensure_ascii=False, indent=2)
        print("✗ 意外成功：直接序列化应该失败")
        return False
    except TypeError as e:
        print(f"✓ 预期失败：{e}")

    print("\n" + "=" * 50)
    print("测试 prepare_for_json_logging 修复:")
    print("=" * 50)

    # 测试所有三个函数
    functions_to_test = [
        ("analyse_appendix", prepare_for_json_logging),
        ("analyse_appendix_asc", prepare_asc),
        ("analyse_noappendix", prepare_no),
    ]

    for module_name, func in functions_to_test:
        try:
            # 使用函数处理数据
            processed_data = func(test_data)

            # 尝试序列化处理后的数据
            json_str = json.dumps(processed_data, ensure_ascii=False, indent=2)

            print(f"✓ {module_name}: 成功处理并序列化数据")

            # 验证 datetime 对象已转换为字符串
            if isinstance(processed_data["source_create_time"], str):
                print(
                    f"  - datetime 对象已转换为字符串: {processed_data['source_create_time']}"
                )
            else:
                print(f"  ✗ datetime 对象未正确转换")
                return False

        except Exception as e:
            print(f"✗ {module_name}: 处理失败 - {e}")
            return False

    print("\n" + "=" * 50)
    print("所有测试通过！datetime JSON 序列化问题已修复")
    print("=" * 50)
    return True


if __name__ == "__main__":
    success = test_prepare_for_json_logging()
    if success:
        print("\n🎉 修复验证成功！代码可以正常运行了。")
        sys.exit(0)
    else:
        print("\n❌ 修复验证失败，请检查代码。")
        sys.exit(1)
