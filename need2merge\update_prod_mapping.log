2025-07-28 10:49:46,507 - INFO - ============================================================
2025-07-28 10:49:46,507 - INFO - 开始正式环境 ES 字段映射更新任务
2025-07-28 10:49:46,509 - INFO - ============================================================
2025-07-28 10:49:46,517 - INFO - ✓ 使用ES主机: http://172.18.7.2:9200
2025-07-28 10:49:46,524 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3_nested_20250725_131633
2025-07-28 10:49:46,549 - INFO - 当前 insert_time 字段类型: text
2025-07-28 10:49:46,550 - WARNING - ⚠️  注意：这是正式环境操作，会重建索引！
2025-07-28 10:49:46,550 - INFO - 当前索引: markersweb_attachment_analysis_v3_nested_20250725_131633
2025-07-28 10:49:46,570 - INFO - 字段类型: text -> date
2025-07-28 10:50:37,558 - INFO - 新索引名: markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037
2025-07-28 10:50:37,559 - INFO - 准备重建索引 markersweb_attachment_analysis_v3_nested_20250725_131633，修改 insert_time 字段类型为 date
2025-07-28 10:50:37,606 - INFO - 已更新 insert_time 字段映射为 date 类型
2025-07-28 10:50:37,783 - INFO - 新索引 markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037 创建成功
2025-07-28 10:50:48,679 - ERROR - Reindex请求失败，状态码: 400
2025-07-28 10:50:48,680 - ERROR - 错误响应: {"took":10839,"timed_out":false,"total":17646,"updated":0,"created":17639,"deleted":0,"batches":18,"version_conflicts":0,"noops":0,"retries":{"bulk":0,"search":0},"throttled_millis":0,"requests_per_second":-1.0,"throttled_until_millis":0,"failures":[{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"qmGvTpgBsUtJ06NfPgaq_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'qmGvTpgBsUtJ06NfPgaq_0'. Preview of field's value: '2025-07-28T09:45:40.540709'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T09:45:40.540709] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"UaevTpgBfqDgtia4VgcE_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'UaevTpgBfqDgtia4VgcE_0'. Preview of field's value: '2025-07-28T09:45:50.391998'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T09:45:50.391998] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"pmGvTpgBsUtJ06NfGwYd_main_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'pmGvTpgBsUtJ06NfGwYd_main_0'. Preview of field's value: '2025-07-28T10:38:36.827160'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T10:38:36.827160] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"pmGvTpgBsUtJ06NfGwYd_main_1","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'pmGvTpgBsUtJ06NfGwYd_main_1'. Preview of field's value: '2025-07-28T10:38:36.827160'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T10:38:36.827160] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"94uvTpgBGyYixO6vJ57__main_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id '94uvTpgBGyYixO6vJ57__main_0'. Preview of field's value: '2025-07-28T10:38:47.577426'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T10:38:47.577426] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"TaevTpgBfqDgtia4MwdH_main_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'TaevTpgBfqDgtia4MwdH_main_0'. Preview of field's value: '2025-07-28T10:38:58.002167'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T10:38:58.002167] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400},{"index":"markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105037","type":"_doc","id":"J4uvTpgBGyYixO6vSp83_main_0","cause":{"type":"mapper_parsing_exception","reason":"failed to parse field [insert_time] of type [date] in document with id 'J4uvTpgBGyYixO6vSp83_main_0'. Preview of field's value: '2025-07-28T10:39:24.446580'","caused_by":{"type":"illegal_argument_exception","reason":"failed to parse date field [2025-07-28T10:39:24.446580] with format [yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis]","caused_by":{"type":"date_time_parse_exception","reason":"date_time_parse_exception: Failed to parse with all enclosed parsers"}}},"status":400}]}
2025-07-28 10:50:48,681 - ERROR - 重建索引失败
2025-07-28 10:52:29,865 - INFO - ============================================================
2025-07-28 10:52:29,865 - INFO - 开始正式环境 ES 字段映射更新任务
2025-07-28 10:52:29,865 - INFO - ============================================================
2025-07-28 10:52:29,875 - INFO - ✓ 使用ES主机: http://172.18.7.2:9200
2025-07-28 10:52:29,884 - INFO - 当前别名 markersweb_attachment_analysis_alias 指向索引: markersweb_attachment_analysis_v3_nested_20250725_131633
2025-07-28 10:52:29,896 - INFO - 当前 insert_time 字段类型: text
2025-07-28 10:52:29,898 - WARNING - ⚠️  注意：这是正式环境操作，会重建索引！
2025-07-28 10:52:29,898 - INFO - 当前索引: markersweb_attachment_analysis_v3_nested_20250725_131633
2025-07-28 10:52:29,898 - INFO - 字段类型: text -> date
2025-07-28 10:52:41,917 - INFO - 新索引名: markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105241
2025-07-28 10:52:41,918 - INFO - 准备重建索引 markersweb_attachment_analysis_v3_nested_20250725_131633，修改 insert_time 字段类型为 date
2025-07-28 10:52:41,940 - INFO - 已更新 insert_time 字段映射为 date 类型，支持多种时间格式
2025-07-28 10:52:42,139 - INFO - 新索引 markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105241 创建成功
2025-07-28 10:52:51,142 - INFO - 数据reindex到 markersweb_attachment_analysis_v3_nested_20250725_131633_date_20250728_105241 完成
2025-07-28 10:52:51,142 - INFO - 处理结果: {'took': 8972, 'timed_out': False, 'total': 17646, 'updated': 0, 'created': 17646, 'deleted': 0, 'batches': 18, 'version_conflicts': 0, 'noops': 0, 'retries': {'bulk': 0, 'search': 0}, 'throttled_millis': 0, 'requests_per_second': -1.0, 'throttled_until_millis': 0, 'failures': []}
2025-07-28 10:52:51,158 - INFO - 数据验证: 原索引 17,646 条，新索引 16,000 条
2025-07-28 10:52:51,158 - WARNING - 新旧索引数据量不一致，请检查
