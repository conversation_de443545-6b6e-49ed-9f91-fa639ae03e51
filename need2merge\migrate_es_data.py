#!/usr/bin/env python3
"""
ES数据迁移脚本

将测试环境的ES索引和数据复制到生产环境
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

import requests
from requests.auth import HTTPBasicAuth
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置信息
CONFIG = {
    # 生产环境ES集群配置
    "SRC_ES_HOST": [
        "http://**********:9200",
        "http://**********:9200",
        "http://**********:9200",
    ],
    "SRC_ES_USER": "elastic",
    "SRC_ES_PASS": "W8DOwJ2xs4mBV4BcNBNi",
    # 测试ES集群配置
    "DST_ES_HOST": "http://***********:9200",
    "DST_ES_USER": "elastic",
    "DST_ES_PASS": "elastic",
    # 要迁移的索引
    "INDICES_TO_MIGRATE": ["markersweb_attachment_analysis_v3"],
    # 批处理大小
    "BATCH_SIZE": 1000,
    # 超时设置
    "TIMEOUT": 30,
    # 重试次数
    "MAX_RETRIES": 3,
}


class ESMigrator:
    """ES数据迁移器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.src_hosts = config["SRC_ES_HOST"]
        self.src_auth = HTTPBasicAuth(config["SRC_ES_USER"], config["SRC_ES_PASS"])
        self.dst_host = config["DST_ES_HOST"]
        self.dst_auth = HTTPBasicAuth(config["DST_ES_USER"], config["DST_ES_PASS"])
        self.timeout = config["TIMEOUT"]
        self.max_retries = config["MAX_RETRIES"]

        # 统计信息
        self.stats = {
            "indices_migrated": 0,
            "documents_migrated": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None,
        }

    def _get_available_src_host(self) -> Optional[str]:
        """获取可用的源ES主机"""
        for host in self.src_hosts:
            try:
                response = requests.get(
                    f"{host}/_cluster/health",
                    auth=self.src_auth,
                    timeout=5,
                    verify=False,
                )
                if response.status_code == 200:
                    print(f"✓ 使用源ES主机: {host}")
                    return host
            except Exception as e:
                print(f"✗ 源ES主机 {host} 不可用: {e}")
                continue
        return None

    def _test_connections(self) -> bool:
        """测试ES连接"""
        print("=" * 60)
        print("测试ES连接")
        print("=" * 60)

        # 测试源ES连接
        src_host = self._get_available_src_host()
        if not src_host:
            print("❌ 所有源ES主机都不可用")
            return False

        self.src_host = src_host

        # 测试目标ES连接
        try:
            response = requests.get(
                f"{self.dst_host}/_cluster/health",
                auth=self.dst_auth,
                timeout=self.timeout,
                verify=False,
            )
            if response.status_code == 200:
                print(f"✓ 目标ES连接正常: {self.dst_host}")
            else:
                print(f"❌ 目标ES连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 目标ES连接异常: {e}")
            return False

        return True

    def _get_index_mapping(self, index_name: str) -> Optional[Dict[str, Any]]:
        """获取索引映射"""
        try:
            response = requests.get(
                f"{self.dst_host}/{index_name}/_mapping",
                auth=self.dst_auth,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None
            else:
                print(f"❌ 获取索引映射失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取索引映射异常: {e}")
            return None

    def _get_index_settings(self, index_name: str) -> Optional[Dict[str, Any]]:
        """获取索引设置"""
        try:
            response = requests.get(
                f"{self.dst_host}/{index_name}/_settings",
                auth=self.dst_auth,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None
            else:
                print(f"❌ 获取索引设置失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取索引设置异常: {e}")
            return None

    def _create_index_in_production(self, index_name: str) -> bool:
        """在生产环境创建索引"""
        print(f"\n--- 创建生产环境索引: {index_name} ---")

        # 获取测试环境的索引映射和设置
        try:
            # 获取映射
            mapping_response = requests.get(
                f"{self.dst_host}/{index_name}/_mapping",
                auth=self.dst_auth,
                timeout=self.timeout,
                verify=False,
            )

            # 获取设置
            settings_response = requests.get(
                f"{self.dst_host}/{index_name}/_settings",
                auth=self.dst_auth,
                timeout=self.timeout,
                verify=False,
            )

            if (
                mapping_response.status_code != 200
                or settings_response.status_code != 200
            ):
                print(f"❌ 无法获取测试环境索引 {index_name} 的配置")
                return False

            mapping_data = mapping_response.json()
            settings_data = settings_response.json()

            # 提取映射和设置
            index_mapping = mapping_data[index_name]["mappings"]
            index_settings = settings_data[index_name]["settings"]["index"]

            # 清理设置中的系统字段
            cleaned_settings = {}
            for key, value in index_settings.items():
                if not key.startswith(
                    ("uuid", "version", "provided_name", "creation_date")
                ):
                    cleaned_settings[key] = value

            # 构建创建索引的请求体
            create_body = {
                "settings": {"index": cleaned_settings},
                "mappings": index_mapping,
            }

            # 在生产环境创建索引
            response = requests.put(
                f"{self.src_host}/{index_name}",
                auth=self.src_auth,
                json=create_body,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code in [200, 201]:
                print(f"✓ 生产环境索引 {index_name} 创建成功")
                return True
            elif response.status_code == 400:
                error_data = response.json()
                if "resource_already_exists_exception" in str(error_data):
                    print(f"⚠️ 生产环境索引 {index_name} 已存在")
                    return True
                else:
                    print(f"❌ 创建索引失败: {error_data}")
                    return False
            else:
                print(f"❌ 创建索引失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 创建索引异常: {e}")
            return False

    def _get_document_count(
        self, host: str, auth: HTTPBasicAuth, index_name: str
    ) -> int:
        """获取文档数量"""
        try:
            response = requests.get(
                f"{host}/{index_name}/_count",
                auth=auth,
                timeout=self.timeout,
                verify=False,
            )

            if response.status_code == 200:
                return response.json()["count"]
            else:
                return 0

        except Exception as e:
            print(f"❌ 获取文档数量异常: {e}")
            return 0

    def _migrate_documents(self, index_name: str) -> bool:
        """迁移文档数据"""
        print(f"\n--- 迁移文档数据: {index_name} ---")

        # 获取源文档数量
        src_count = self._get_document_count(self.dst_host, self.dst_auth, index_name)
        print(f"测试环境文档数量: {src_count}")

        if src_count == 0:
            print("⚠️ 测试环境没有文档数据")
            return True

        # 获取目标文档数量
        dst_count = self._get_document_count(self.src_host, self.src_auth, index_name)
        print(f"生产环境文档数量: {dst_count}")

        migrated_count = 0
        scroll_id = None

        try:
            # 初始化滚动搜索
            scroll_response = requests.post(
                f"{self.dst_host}/{index_name}/_search?scroll=5m",
                auth=self.dst_auth,
                json={
                    "size": self.config["BATCH_SIZE"],
                    "query": {"match_all": {}},
                    "_source": True,
                },
                timeout=self.timeout,
                verify=False,
            )

            if scroll_response.status_code != 200:
                print(f"❌ 初始化滚动搜索失败: {scroll_response.status_code}")
                return False

            scroll_data = scroll_response.json()
            scroll_id = scroll_data.get("_scroll_id")
            hits = scroll_data.get("hits", {}).get("hits", [])

            while hits:
                # 准备批量插入数据
                bulk_data = []

                for hit in hits:
                    # 构建索引操作
                    index_action = {"index": {"_index": index_name, "_id": hit["_id"]}}

                    bulk_data.append(json.dumps(index_action))
                    bulk_data.append(json.dumps(hit["_source"]))

                # 执行批量插入
                bulk_body = "\n".join(bulk_data) + "\n"

                bulk_response = requests.post(
                    f"{self.src_host}/_bulk",
                    auth=self.src_auth,
                    data=bulk_body,
                    headers={"Content-Type": "application/x-ndjson"},
                    timeout=self.timeout * 2,
                    verify=False,
                )

                if bulk_response.status_code == 200:
                    bulk_result = bulk_response.json()
                    errors = bulk_result.get("errors", False)

                    if not errors:
                        migrated_count += len(hits)
                        print(f"✓ 已迁移 {migrated_count}/{src_count} 个文档")
                    else:
                        print(f"⚠️ 批量插入有错误")
                        for item in bulk_result.get("items", []):
                            if "index" in item and "error" in item["index"]:
                                print(f"   错误: {item['index']['error']}")
                                self.stats["errors"] += 1
                else:
                    print(f"❌ 批量插入失败: {bulk_response.status_code}")
                    self.stats["errors"] += 1

                # 继续滚动搜索
                if scroll_id:
                    scroll_response = requests.post(
                        f"{self.dst_host}/_search/scroll",
                        auth=self.dst_auth,
                        json={"scroll": "5m", "scroll_id": scroll_id},
                        timeout=self.timeout,
                        verify=False,
                    )

                    if scroll_response.status_code == 200:
                        scroll_data = scroll_response.json()
                        scroll_id = scroll_data.get("_scroll_id")
                        hits = scroll_data.get("hits", {}).get("hits", [])
                    else:
                        print(f"❌ 滚动搜索失败: {scroll_response.status_code}")
                        break
                else:
                    break

            # 清理滚动上下文
            if scroll_id:
                requests.delete(
                    f"{self.dst_host}/_search/scroll",
                    auth=self.dst_auth,
                    json={"scroll_id": [scroll_id]},
                    timeout=self.timeout,
                    verify=False,
                )

            print(f"✓ 索引 {index_name} 迁移完成: {migrated_count} 个文档")
            self.stats["documents_migrated"] += migrated_count
            return True

        except Exception as e:
            print(f"❌ 迁移文档异常: {e}")
            self.stats["errors"] += 1
            return False

    def _verify_migration(self, index_name: str) -> bool:
        """验证迁移结果"""
        print(f"\n--- 验证迁移结果: {index_name} ---")

        try:
            # 获取源和目标的文档数量
            src_count = self._get_document_count(
                self.dst_host, self.dst_auth, index_name
            )
            dst_count = self._get_document_count(
                self.src_host, self.src_auth, index_name
            )

            print(f"测试环境文档数量: {src_count}")
            print(f"生产环境文档数量: {dst_count}")

            if dst_count >= src_count:
                print(f"✓ 索引 {index_name} 验证通过")
                return True
            else:
                print(f"❌ 索引 {index_name} 验证失败: 文档数量不匹配")
                return False

        except Exception as e:
            print(f"❌ 验证异常: {e}")
            return False

    def migrate_index(self, index_name: str) -> bool:
        """迁移单个索引"""
        print(f"\n{'='*60}")
        print(f"开始迁移索引: {index_name}")
        print(f"{'='*60}")

        # 1. 在生产环境创建索引
        if not self._create_index_in_production(index_name):
            return False

        # 2. 迁移文档数据
        if not self._migrate_documents(index_name):
            return False

        # 3. 验证迁移结果
        if not self._verify_migration(index_name):
            return False

        self.stats["indices_migrated"] += 1
        print(f"✓ 索引 {index_name} 迁移成功")
        return True

    def run_migration(self) -> bool:
        """执行完整迁移"""
        print("=" * 60)
        print("ES数据迁移开始")
        print("=" * 60)
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试环境: {self.config['DST_ES_HOST']}")
        print(f"生产环境: {self.config['SRC_ES_HOST']}")
        print(f"迁移索引: {', '.join(self.config['INDICES_TO_MIGRATE'])}")

        self.stats["start_time"] = time.time()

        # 测试连接
        if not self._test_connections():
            return False

        # 迁移每个索引
        success_count = 0
        total_count = len(self.config["INDICES_TO_MIGRATE"])

        for index_name in self.config["INDICES_TO_MIGRATE"]:
            try:
                if self.migrate_index(index_name):
                    success_count += 1
                else:
                    print(f"❌ 索引 {index_name} 迁移失败")
            except Exception as e:
                print(f"❌ 索引 {index_name} 迁移异常: {e}")
                self.stats["errors"] += 1

        self.stats["end_time"] = time.time()

        # 输出迁移结果
        self._print_migration_summary(success_count, total_count)

        return success_count == total_count

    def _print_migration_summary(self, success_count: int, total_count: int):
        """打印迁移摘要"""
        duration = self.stats["end_time"] - self.stats["start_time"]

        print("\n" + "=" * 60)
        print("迁移结果摘要")
        print("=" * 60)
        print(f"总索引数: {total_count}")
        print(f"成功迁移: {success_count}")
        print(f"失败数量: {total_count - success_count}")
        print(f"文档迁移: {self.stats['documents_migrated']}")
        print(f"错误数量: {self.stats['errors']}")
        print(f"耗时: {duration:.2f} 秒")

        if success_count == total_count:
            print("🎉 所有索引迁移成功！")
        else:
            print("❌ 部分索引迁移失败")


def main():
    """主函数"""
    print("ES数据迁移工具")
    print("将测试环境数据复制到生产环境")

    # 确认操作
    print(f"\n⚠️  即将执行以下操作:")
    print(f"   源环境(测试): {CONFIG['DST_ES_HOST']}")
    print(f"   目标环境(生产): {CONFIG['SRC_ES_HOST']}")
    print(f"   迁移索引: {', '.join(CONFIG['INDICES_TO_MIGRATE'])}")

    confirm = input("\n确认执行迁移? (输入 'YES' 确认): ")
    if confirm != "YES":
        print("❌ 操作已取消")
        return 1

    # 执行迁移
    migrator = ESMigrator(CONFIG)

    try:
        success = migrator.run_migration()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 迁移过程异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
