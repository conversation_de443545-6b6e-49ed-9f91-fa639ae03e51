#!/usr/bin/env python3
"""
生产环境索引验证脚本
用于清理前后的状态检查
"""

import os
from dotenv import load_dotenv
from elasticsearch import Elasticsearch

# 加载环境变量
load_dotenv()

# 标准字段列表
STANDARD_FIELDS = {
    # 业务数据字段（40个）
    "bid_name",
    "bid_number",
    "bid_budget",
    "fiscal_delegation_number",
    "prj_addr",
    "prj_name",
    "prj_number",
    "prj_type",
    "release_time",
    "prj_approval_authority",
    "superintendent_office",
    "superintendent_office_code",
    "tenderee",
    "bid_submission_deadline",
    "trade_platform",
    "procurement_method",
    "prj_sub_type",
    "province",
    "city",
    "county",
    "announcement_type",
    "object_name",
    "object_brand",
    "object_model",
    "object_supplier",
    "object_produce_area",
    "object_conf",
    "object_oem",
    "object_amount",
    "object_unit",
    "object_price",
    "object_total_price",
    "object_maintenance_period",
    "object_price_source",
    "object_quality",
    "bidder_price",
    "bidder_name",
    "bidder_contact_person",
    "bidder_contact_phone_number",
    "bidder_contract_config_param",
    "agent",
    "service_fee",
    "bid_cancelled_flag",
    "bid_cancelled_reason",
    # 源数据元数据字段（6个）
    "source_id",
    "source_title",
    "source_create_time",
    "source_category",
    "source_url",
    "source_appendix",
    "appendix_info",
    # 附件相关字段（8个）
    "bid_doc_name",
    "bid_doc_ext",
    "bid_doc_link_out",
    "bid_doc_link_key",
    "contract_name",
    "contract_ext",
    "contract_link_out",
    "contract_link_key",
    # 系统字段（1个）
    "insert_time",
}


def setup_production_es():
    """设置生产环境ES连接"""
    es_host = os.getenv("ES_HOST", "http://**********:9200")
    es_user = os.getenv("ES_USER", "elastic")
    es_password = os.getenv("ES_PASSWORD", "W8DOwJ2xs4mBV4BcNBNi")

    try:
        # 尝试新版本的参数
        es = Elasticsearch(
            [es_host], basic_auth=(es_user, es_password), request_timeout=30
        )
    except TypeError:
        # 兼容旧版本
        es = Elasticsearch([es_host], http_auth=(es_user, es_password))

    return es


def verify_production_index():
    """验证生产环境索引状态"""
    es = setup_production_es()
    index_name = os.getenv(
        "ES_INDEX_ANALYSIS_ALIAS", "markersweb_attachment_analysis_alias"
    )

    print("生产环境索引验证")
    print("=" * 50)

    try:
        # 基本信息
        es_host = os.getenv("ES_HOST", "http://**********:9200")
        print(f"ES主机: {es_host}")
        print(f"索引名称: {index_name}")

        # 测试连接
        info = es.info()
        print(f"✓ ES版本: {info['version']['number']}")

        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            print(f"✗ 索引 {index_name} 不存在")
            return

        # 强制刷新索引
        es.indices.refresh(index=index_name)
        print("✓ 索引已刷新")

        # 获取文档总数
        count_response = es.count(index=index_name)
        total_docs = count_response["count"]
        print(f"文档总数: {total_docs:,}")

        # 分析文档字段（使用较大样本）
        print(f"\n分析文档字段（样本：10000个文档）...")
        sample_response = es.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10000}
        )

        all_doc_fields = set()
        nonstandard_count = {}

        for doc in sample_response["hits"]["hits"]:
            doc_fields = set(doc["_source"].keys())
            all_doc_fields.update(doc_fields)

            # 统计非标准字段
            for field in doc_fields:
                if field not in STANDARD_FIELDS:
                    nonstandard_count[field] = nonstandard_count.get(field, 0) + 1

        doc_nonstandard = all_doc_fields - STANDARD_FIELDS

        print(f"样本文档数: {len(sample_response['hits']['hits'])}")
        print(f"文档中总字段数: {len(all_doc_fields)}")
        print(f"标准字段数: {len(all_doc_fields & STANDARD_FIELDS)}")
        print(f"非标准字段数: {len(doc_nonstandard)}")

        if doc_nonstandard:
            print(f"\n非标准字段详情:")
            for field in sorted(doc_nonstandard):
                count = nonstandard_count.get(field, 0)
                percentage = (count / len(sample_response["hits"]["hits"])) * 100
                print(
                    f"  - {field}: {count}/{len(sample_response['hits']['hits'])} 文档 ({percentage:.1f}%)"
                )
        else:
            print("✓ 文档中没有非标准字段")

        # 检查映射
        print(f"\n分析索引映射...")
        mapping = es.indices.get_mapping(index=index_name)

        mapping_fields = set()
        for index, mapping_data in mapping.items():
            properties = mapping_data.get("mappings", {}).get("properties", {})
            mapping_fields.update(properties.keys())

        mapping_nonstandard = mapping_fields - STANDARD_FIELDS

        print(f"映射中总字段数: {len(mapping_fields)}")
        print(f"映射中标准字段数: {len(mapping_fields & STANDARD_FIELDS)}")
        print(f"映射中非标准字段数: {len(mapping_nonstandard)}")

        # 总结
        print(f"\n验证结果总结:")

        if not doc_nonstandard:
            print("✅ 文档内容符合标准 - 所有文档只包含标准字段")
        else:
            print(f"❌ 文档内容需要清理 - 发现 {len(doc_nonstandard)} 个非标准字段")

        if mapping_nonstandard:
            print(
                f"⚠️  映射中有 {len(mapping_nonstandard)} 个历史字段定义（不影响功能）"
            )
        else:
            print("✅ 映射也完全符合标准")

        # 建议
        print(f"\n建议:")
        if not doc_nonstandard:
            print("🎉 索引状态良好，无需清理")
        else:
            print("🔧 建议运行 production_cleanup.py 进行清理")

            # 估算需要更新的文档数
            docs_need_update = 0
            for doc in sample_response["hits"]["hits"]:
                doc_fields = set(doc["_source"].keys())
                if doc_fields - STANDARD_FIELDS:
                    docs_need_update += 1

            estimated_total = int(
                (docs_need_update / len(sample_response["hits"]["hits"])) * total_docs
            )
            print(
                f"预估需要更新的文档数: {estimated_total:,} / {total_docs:,} ({(estimated_total/total_docs)*100:.1f}%)"
            )

    except Exception as e:
        print(f"验证失败: {e}")


if __name__ == "__main__":
    verify_production_index()
