#!/usr/bin/env python3
"""
演示文件名"合同包"关键词排除功能

该脚本演示了在 analyse_appendix.py 中新增的文件名排除功能：
当附件文件名包含"合同包"关键词时，即使内容包含合同关键词，也会被判定为非合同文件并跳过LLM分析。
"""

import sys
import os

# 添加当前目录到Python路径，以便导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from analyse_appendix import contains_contract_keywords
from utils.log_cfg import log


def demo_filename_exclusion():
    """演示文件名排除功能"""
    log.info("=" * 60)
    log.info("演示文件名'合同包'关键词排除功能")
    log.info("=" * 60)
    
    # 测试用例：内容包含合同关键词，但文件名包含"合同包"
    test_cases = [
        {
            "content": "这是一份医疗设备采购合同，甲方：某医院，乙方：某公司",
            "filename": "合同包.pdf",
            "description": "内容包含'合同'关键词，但文件名包含'合同包'"
        },
        {
            "content": "医疗设备维护服务协议，服务期限：2023年1月1日至2023年12月31日",
            "filename": "投标合同包.docx", 
            "description": "内容包含'服务协议'关键词，但文件名包含'合同包'"
        },
        {
            "content": "这是一份医疗设备采购合同，甲方：某医院，乙方：某公司",
            "filename": "合同文件.pdf",
            "description": "内容包含'合同'关键词，文件名正常"
        },
        {
            "content": "医疗设备维护服务协议，服务期限：2023年1月1日至2023年12月31日",
            "filename": "服务协议.docx",
            "description": "内容包含'服务协议'关键词，文件名正常"
        },
        {
            "content": "招标文件内容，项目名称：医疗设备采购",
            "filename": "合同包.pdf",
            "description": "内容不包含合同关键词，文件名包含'合同包'"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        log.info(f"\n测试用例 {i}: {case['description']}")
        log.info(f"文件名: {case['filename']}")
        log.info(f"内容预览: {case['content'][:50]}...")
        
        result = contains_contract_keywords(case['content'], filename=case['filename'])
        
        if result:
            log.info(f"✓ 结果: 通过检查，将进行LLM分析")
        else:
            log.info(f"✗ 结果: 被排除，跳过LLM分析")
        
        log.info("-" * 40)
    
    log.info("\n" + "=" * 60)
    log.info("演示完成")
    log.info("=" * 60)


if __name__ == "__main__":
    demo_filename_exclusion()
