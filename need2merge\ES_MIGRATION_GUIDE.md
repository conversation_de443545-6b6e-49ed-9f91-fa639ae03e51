# ES数据迁移指南

## 概述

本指南提供了将测试环境ES数据迁移到生产环境的详细步骤和脚本。

## 环境配置

### 生产环境ES集群
- 主机: **********:9200, 172.18.7.3:9200, 172.18.7.4:9200
- 用户: elastic
- 密码: W8DOwJ2xs4mBV4BcNBNi

### 测试环境ES集群
- 主机: ***********:9200
- 用户: elastic
- 密码: elastic

### 要迁移的索引
- `chn_ylcg` - 主要数据索引
- `markersweb_attachment_analysis_v3` - 分析结果索引

## 迁移脚本

提供了两个迁移脚本：

### 1. 基础迁移脚本 (`migrate_es_data.py`)
- 基本的数据迁移功能
- 适用于简单的迁移场景

### 2. 安全迁移脚本 (`migrate_es_data_safe.py`) **推荐**
- 包含自动备份功能
- 支持回滚操作
- 更完善的错误处理
- 详细的进度显示

## 使用步骤

### 准备工作

1. **检查环境连通性**
   ```bash
   # 测试生产环境连接
   curl -u elastic:W8DOwJ2xs4mBV4BcNBNi http://**********:9200/_cluster/health
   
   # 测试测试环境连接
   curl -u elastic:elastic http://***********:9200/_cluster/health
   ```

2. **检查索引状态**
   ```bash
   # 查看测试环境索引
   curl -u elastic:elastic http://***********:9200/_cat/indices/chn_ylcg,markersweb_attachment_analysis_v3?v
   
   # 查看生产环境索引
   curl -u elastic:W8DOwJ2xs4mBV4BcNBNi http://**********:9200/_cat/indices/chn_ylcg,markersweb_attachment_analysis_v3?v
   ```

3. **安装依赖**
   ```bash
   pip install requests urllib3
   ```

### 执行迁移

**推荐使用安全迁移脚本：**

```bash
python migrate_es_data_safe.py
```

脚本会提示确认操作，按照提示输入确认信息即可。

### 迁移过程

1. **连接测试** - 验证两个环境的ES连接
2. **备份创建** - 自动备份生产环境现有数据
3. **索引创建** - 在生产环境创建新索引
4. **数据迁移** - 批量迁移文档数据
5. **结果验证** - 验证迁移结果的完整性

## 安全特性

### 自动备份
- 迁移前自动备份生产环境数据
- 备份文件保存在 `./es_backup/` 目录
- 包含索引设置、映射和所有文档数据

### 错误处理
- 连接失败自动重试
- 批量操作错误恢复
- 详细的错误日志记录

### 进度监控
- 实时显示迁移进度
- 文档数量统计
- 性能指标监控

## 回滚操作

如果迁移后发现问题，可以使用备份文件进行回滚：

```python
# 示例回滚代码（需要根据实际备份文件调整）
import json
import requests
from requests.auth import HTTPBasicAuth

# 读取备份文件
with open('./es_backup/chn_ylcg_backup_1234567890.json', 'r') as f:
    backup_data = json.load(f)

# 恢复索引...
```

## 监控和验证

### 迁移后检查

1. **文档数量对比**
   ```bash
   # 测试环境
   curl -u elastic:elastic http://***********:9200/chn_ylcg/_count
   
   # 生产环境
   curl -u elastic:W8DOwJ2xs4mBV4BcNBNi http://**********:9200/chn_ylcg/_count
   ```

2. **索引健康状态**
   ```bash
   curl -u elastic:W8DOwJ2xs4mBV4BcNBNi http://**********:9200/_cat/indices/chn_ylcg,markersweb_attachment_analysis_v3?v&h=index,health,status,docs.count
   ```

3. **随机数据抽查**
   ```bash
   # 获取随机文档进行对比
   curl -u elastic:W8DOwJ2xs4mBV4BcNBNi http://**********:9200/chn_ylcg/_search?size=5
   ```

## 注意事项

### 执行前
- ✅ 确保生产环境ES集群状态正常
- ✅ 建议在维护窗口期间执行
- ✅ 通知相关团队成员
- ✅ 准备回滚计划

### 执行中
- ⚠️ 不要中断迁移过程
- ⚠️ 监控ES集群资源使用情况
- ⚠️ 关注错误日志输出

### 执行后
- ✅ 验证数据完整性
- ✅ 测试应用程序功能
- ✅ 监控系统性能
- ✅ 保留备份文件

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连通性
   - 调整超时配置
   - 确认ES服务状态

2. **认证失败**
   - 验证用户名密码
   - 检查用户权限
   - 确认ES安全配置

3. **磁盘空间不足**
   - 检查ES数据目录空间
   - 清理不必要的索引
   - 调整批处理大小

4. **内存不足**
   - 调整ES堆内存设置
   - 减少批处理大小
   - 分批执行迁移

### 日志分析

迁移脚本会输出详细的日志信息：
- ✓ 成功操作
- ⚠️ 警告信息
- ❌ 错误信息

根据日志信息可以快速定位问题。

## 性能优化

### 配置调整
```python
CONFIG = {
    "BATCH_SIZE": 500,      # 批处理大小，可根据性能调整
    "TIMEOUT": 60,          # 超时时间
    "MAX_RETRIES": 3,       # 重试次数
}
```

### 建议设置
- 小数据量: BATCH_SIZE = 1000
- 大数据量: BATCH_SIZE = 200-500
- 网络较慢: 增加 TIMEOUT 值
- 不稳定网络: 增加 MAX_RETRIES 值

## 联系支持

如果在迁移过程中遇到问题，请：
1. 保存完整的错误日志
2. 记录操作步骤
3. 提供环境配置信息
4. 联系技术支持团队