#!/usr/bin/env python
# -*- coding: utf-8 -*-

from elasticsearch import Elasticsearch
import json
import os
import re
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 连接到 Elasticsearch
es_host = os.getenv("ES_HOST", "http://localhost:9200")
es_user = os.getenv("ES_USER", "")
es_password = os.getenv("ES_PASSWORD", "")

# 创建 Elasticsearch 客户端
try:
    if es_user and es_password:
        es = Elasticsearch([es_host], basic_auth=(es_user, es_password))
    else:
        es = Elasticsearch([es_host])

    print(f"成功连接到 Elasticsearch: {es_host}")
except Exception as e:
    print(f"连接 Elasticsearch 失败: {str(e)}")
    exit(1)

# 获取索引名称
index_name = os.getenv("ES_INDEX_ANALYSIS", "markersweb_attachment_analysis_alias")
print(f"使用索引: {index_name}")

# 定义查询 - 查找prj_name为空且source_url以https://www.xinecai.com开头的文档
query = {
    "query": {
        "bool": {
            "must": [
                {"match_phrase_prefix": {"source_url": "https://www.xinecai.com"}},
                {
                    "bool": {
                        "should": [
                            {"bool": {"must_not": {"exists": {"field": "prj_name"}}}},
                            {"term": {"prj_name": ""}},
                        ],
                        "minimum_should_match": 1,
                    }
                },
                {"exists": {"field": "source_title"}},  # 确保source_title字段存在
            ]
        }
    },
    "size": 50,  # 测试模式只处理50个文档
    "_source": ["source_url", "source_title", "prj_name"],
}

# 定义正则表达式模式
# 匹配完整的"中国科大附一院（安徽省立医院）XXX项目"或"中国科学技术大学附属第一医院（安徽省立医院）YYY项目"
# 使用更宽松的模式匹配任何字符，直到"项目"出现
pattern1 = r"(中国科大附一院（安徽省立医院）.*?项目)"
pattern2 = r"(中国科学技术大学附属第一医院（安徽省立医院）.*?项目)"

# 统计变量
total_docs = 0
matched_docs = 0
no_match_docs = 0

try:
    # 执行查询
    response = es.search(index=index_name, body=query)

    hits = response["hits"]["hits"]
    total_hits = response["hits"]["total"]["value"]

    print(f"找到符合条件的文档总数: {total_hits}")
    print(f"本次测试将处理前 {len(hits)} 个文档")

    # 处理文档
    for hit in hits:
        total_docs += 1
        doc_id = hit["_id"]
        source = hit["_source"]
        source_title = source.get("source_title", "")

        # 应用正则表达式
        match = re.search(pattern1, source_title) or re.search(pattern2, source_title)

        if match:
            matched_docs += 1
            project_name = match.group(1)
            print(f"\n文档 {doc_id}:")
            print(f"原始标题: {source_title}")
            print(f"提取的项目名: {project_name}")
            print("✓ 测试模式: 将会更新此文档")
        else:
            no_match_docs += 1
            # 显示所有不匹配的文档，因为只有少数几个
            print(f"\n文档 {doc_id} 标题不匹配模式:")
            print(f"标题: {source_title}")

    # 打印最终统计信息
    print("\n=== 测试完成 ===")
    print(f"总共处理文档数: {total_docs}")
    print(f"匹配成功文档数: {matched_docs}")
    print(f"标题不匹配文档数: {no_match_docs}")
    print(
        f"匹配率: {matched_docs/total_docs*100:.2f}% (如果太低，可能需要调整正则表达式)"
    )

    # 显示一些不匹配的标题示例，帮助调整正则表达式
    if no_match_docs > 0 and matched_docs == 0:
        print("\n=== 正则表达式可能需要调整 ===")
        print("没有找到任何匹配的标题。以下是一些标题示例，可以帮助调整正则表达式:")
        for i, hit in enumerate(hits[:5]):
            title = hit["_source"].get("source_title", "")
            print(f"{i+1}. {title}")

except Exception as e:
    print(f"处理过程中出错: {str(e)}")
    exit(1)
